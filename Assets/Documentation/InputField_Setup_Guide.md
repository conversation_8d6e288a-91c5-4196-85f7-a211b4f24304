# InputField设置指南

## 问题说明
如果你无法将InputField拖入PasswordPuzzleUI组件的对应字段，通常是因为InputField类型不匹配。

## 解决方案

### 方案1：使用Legacy InputField（推荐用于简单项目）

1. **创建Legacy InputField**：
   ```
   右键点击Canvas → UI → Legacy → Input Field
   ```

2. **设置PasswordPuzzleUI**：
   - 将创建的InputField拖入 `Legacy Password Input` 字段
   - 保持 `Tmp Password Input` 字段为空

### 方案2：使用TextMeshPro InputField（推荐用于新项目）

1. **导入TextMeshPro**：
   ```
   Window → TextMeshPro → Import TMP Essential Resources
   ```

2. **创建TextMeshPro InputField**：
   ```
   右键点击Canvas → UI → Input Field - TextMeshPro
   ```

3. **设置PasswordPuzzleUI**：
   - 将创建的TMP InputField拖入 `Tmp Password Input` 字段
   - 保持 `Legacy Password Input` 字段为空

## 详细创建步骤

### Legacy InputField创建步骤
```
1. 右键点击Canvas
2. 选择 UI → Legacy → Input Field
3. 重命名为 "PasswordInput"
4. 设置属性：
   - Content Type: Standard
   - Line Type: Single Line
   - Character Validation: None（或Integer Number用于纯数字）
   - Character Limit: 0（无限制）或设置具体数字
```

### TextMeshPro InputField创建步骤
```
1. 确保已导入TextMeshPro资源
2. 右键点击Canvas
3. 选择 UI → Input Field - TextMeshPro
4. 重命名为 "PasswordInput"
5. 设置属性：
   - Content Type: Standard
   - Line Type: Single Line
   - Character Validation: None（或Integer用于纯数字）
   - Character Limit: 0（无限制）或设置具体数字
```

## 验证设置

### 检查InputField类型
1. 选中你创建的InputField
2. 在Inspector中查看组件类型：
   - Legacy: `Input Field (Script)`
   - TextMeshPro: `TMP - Input Field (Script)`

### 测试拖拽
1. 选中PasswordPuzzleUI组件
2. 尝试将InputField拖入对应字段：
   - Legacy InputField → `Legacy Password Input`
   - TMP InputField → `Tmp Password Input`

## 常见问题

### Q: 仍然无法拖入
**可能原因**：
- InputField类型错误
- PasswordPuzzleUI脚本编译错误
- Unity版本不支持TextMeshPro

**解决方法**：
1. 检查Console是否有编译错误
2. 重新创建正确类型的InputField
3. 重启Unity编辑器

### Q: 两种InputField有什么区别？
**Legacy InputField**：
- Unity内置的传统UI系统
- 功能基础但稳定
- 适合简单项目

**TextMeshPro InputField**：
- 更现代的文本渲染系统
- 支持更好的字体渲染和特效
- 推荐用于新项目

### Q: 可以同时使用两种InputField吗？
**不建议**：
- PasswordPuzzleUI设计为使用其中一种
- 同时设置两个可能导致冲突
- 选择一种并保持另一个字段为空

## 推荐设置

### 对于密码输入框的特殊设置

#### 隐藏密码字符（可选）
```csharp
// 在InputField组件中设置
Content Type: Password
```

#### 限制输入长度
```csharp
// 设置Character Limit
Character Limit: 4  // 对于4位密码
```

#### 只允许数字输入
```csharp
// 设置Character Validation
Character Validation: Integer
```

#### 自动聚焦
```csharp
// PasswordPuzzleUI会自动处理
// 显示面板时自动激活输入框
```

## 完整UI层级结构示例

```
Canvas
└── PasswordPanel (Panel)
    ├── TitleText (Text/TextMeshPro)
    ├── HintText (Text/TextMeshPro)
    ├── PasswordInput (InputField 或 TMP_InputField)
    │   ├── Placeholder (Text/TextMeshPro)
    │   └── Text (Text/TextMeshPro)
    ├── ButtonPanel (Panel)
    │   ├── ConfirmButton (Button)
    │   └── CancelButton (Button)
    └── StatusText (Text/TextMeshPro)
```

## 最终检查清单

- [ ] InputField类型正确（Legacy或TMP）
- [ ] 拖入了正确的字段
- [ ] 另一个InputField字段保持为空
- [ ] 没有编译错误
- [ ] InputField有正确的子对象（Text、Placeholder）
- [ ] 可以在运行时正常输入文字
