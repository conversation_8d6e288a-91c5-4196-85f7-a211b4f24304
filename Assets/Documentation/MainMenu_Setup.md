# 主菜单设置指南

## 概述
MainMenuController脚本用于处理开始界面的按键功能，与VideoController配合播放开场视频。

## 功能特性

### 🎮 核心功能
- **开始按钮**：隐藏主菜单UI并播放开场视频
- **退出按钮**：退出游戏
- **视频播放**：与VideoController集成播放开场视频
- **Fungus集成**：视频播放完成后触发指定的Fungus Block

### ⌨️ 键盘支持
- **ESC键**：在主菜单时退出游戏，播放视频时跳过视频，其他时候返回主菜单
- **空格键**：跳过正在播放的视频

## 设置步骤

### 1. 创建主菜单UI
```
Canvas
└── MainMenuPanel
    ├── TitleText (Text) - 游戏标题
    ├── StartButton (Button) - 开始游戏
    │   └── Text - "开始游戏"
    ├── ExitButton (Button) - 退出游戏
    │   └── Text - "退出游戏"
    └── Background (Image) - 背景图片
```

### 2. 配置MainMenuController组件

#### Inspector设置
```
UI组件:
- Main Menu UI: 拖入MainMenuPanel
- Start Button: 拖入开始按钮
- Exit Button: 拖入退出按钮

视频控制:
- Video Controller: 拖入VideoController组件
- Opening Video Index: 0 (开场视频在VideoController中的索引)

Fungus集成:
- Flowchart: 拖入Flowchart组件
- After Video Block Name: "AfterOpeningVideo" (视频播放完成后执行的Block名称)

设置:
- Enable Debug Log: ✓ (启用调试日志)
- Exit Confirm Delay: 0.5 (退出确认延迟)
```

### 3. 配置VideoController
确保VideoController已正确设置：
```
Video Clips:
- Element 0: 开场视频片段
- Element 1: 其他视频片段（如果有）

其他设置按照VideoController文档配置
```

### 4. 创建Fungus Block
在Flowchart中创建视频播放完成后要执行的Block：

#### 示例Block: "AfterOpeningVideo"
```
Commands:
1. Say: "欢迎来到游戏世界..."
2. Wait: 2秒
3. Load Scene: "GameScene"
```

#### 或者更复杂的逻辑：
```
Commands:
1. Set Variable: GameStarted = true
2. Say: "开场视频播放完成"
3. Menu: "开始游戏" -> 跳转到StartGame Block
4. Menu: "返回主菜单" -> 调用MainMenuController.ShowMainMenu()
```

## 工作流程

### 正常流程
1. **游戏启动** → 显示主菜单界面
2. **点击开始按钮** → 隐藏主菜单UI
3. **播放开场视频** → VideoController播放指定视频
4. **视频播放完成** → 触发指定的Fungus Block
5. **点击退出按钮** → 退出游戏

### 可选流程
- **按ESC键** → 在主菜单时退出游戏，播放视频时跳过视频，其他时候返回主菜单
- **按空格键** → 跳过正在播放的视频
- **调用ReturnToMainMenu()** → 从其他脚本返回主菜单

## 代码API

### 公共方法
```csharp
// 按钮事件
OnStartButtonClicked()     // 开始按钮点击
OnExitButtonClicked()      // 退出按钮点击

// UI控制
ShowMainMenu()             // 显示主菜单
ReturnToMainMenu()         // 返回主菜单

// 视频控制
SkipVideo()                // 跳过视频

// 设置方法
SetOpeningVideoIndex(int)      // 设置开场视频索引
SetAfterVideoBlockName(string) // 设置视频完成后的Block名称
```

### 在其他脚本中使用
```csharp
// 获取MainMenuController引用
MainMenuController mainMenu = FindObjectOfType<MainMenuController>();

// 返回主菜单
mainMenu.ReturnToMainMenu();

// 设置不同的开场视频
mainMenu.SetOpeningVideoIndex(1);
```

## 与VideoController的配合

### 视频播放流程
1. **MainMenuController调用**：`videoController.ShowVideoScreen()`
2. **播放指定视频**：`videoController.PlayVideo(openingVideoIndex)`
3. **等待播放完成**：监听`videoPlayer.isPlaying`
4. **隐藏视频屏幕**：`videoController.HideVideoScreen()`
5. **触发Fungus Block**：`flowchart.ExecuteBlock(afterVideoBlockName)`

### 视频索引管理
- **Opening Video Index = 0**：播放VideoController中的第一个视频
- **Opening Video Index = 1**：播放VideoController中的第二个视频
- 确保索引不超出VideoController中视频片段的数量

## 常见问题

### Q: 点击开始按钮没有反应
**检查项目**:
- MainMenuController组件是否正确添加
- Start Button字段是否已设置
- VideoController是否正确配置
- Console中是否有错误信息

### Q: 视频播放完成后没有加载游戏场景
**检查项目**:
- Game Scene Name是否正确设置
- 场景是否已添加到Build Settings
- Load Scene After Video是否勾选
- VideoController的视频是否正常播放完成

### Q: 退出按钮不工作
**检查项目**:
- Exit Button字段是否已设置
- 在编辑器中退出功能正常（会停止播放）
- 在构建版本中会调用Application.Quit()

### Q: ESC键功能不正常
**检查项目**:
- MainMenuController的Update方法是否正常执行
- Main Menu UI的activeInHierarchy状态是否正确

## 扩展功能

### 添加设置菜单
```csharp
[SerializeField] private Button settingsButton;
[SerializeField] private GameObject settingsPanel;

public void OnSettingsButtonClicked()
{
    settingsPanel.SetActive(true);
    mainMenuUI.SetActive(false);
}
```

### 添加加载界面
```csharp
[SerializeField] private GameObject loadingPanel;

void LoadGameScene()
{
    loadingPanel.SetActive(true);
    SceneManager.LoadSceneAsync(gameSceneName);
}
```

### 添加音效
```csharp
[SerializeField] private AudioSource audioSource;
[SerializeField] private AudioClip buttonClickSound;

public void OnStartButtonClicked()
{
    audioSource.PlayOneShot(buttonClickSound);
    // ... 其他逻辑
}
```

## 测试清单

- [ ] 开始按钮正常工作
- [ ] 退出按钮正常工作
- [ ] 主菜单UI正确隐藏/显示
- [ ] 开场视频正常播放
- [ ] 视频播放完成后正确加载游戏场景
- [ ] ESC键功能正常
- [ ] 空格键跳过视频功能正常
- [ ] 没有控制台错误信息
