# 密码谜题 - Fungus设置指南

## 概述
本指南详细说明如何使用Fungus插件创建密码输入谜题系统。

## 1. 创建Flowchart

1. 在场景中创建一个空的GameObject，命名为"PasswordPuzzleFlowchart"
2. 添加Flowchart组件
3. 在Flowchart中创建以下变量：

### 必需变量
- **CorrectPassword** (String) - 存储正确密码
- **UserInput** (String) - 存储用户输入
- **PuzzleSolved** (Boolean) - 谜题解决状态
- **AttemptCount** (Integer) - 尝试次数计数

## 2. 创建Block结构

### Block 1: InitPasswordPuzzle
**用途**: 初始化密码谜题
**Commands**:
1. **Set Variable** - 设置AttemptCount = 0
2. **Say** - 显示谜题介绍文本

### Block 2: CheckPassword
**用途**: 检查用户输入的密码
**Commands**:
1. **If** - 条件: UserInput == CorrectPassword
   - **Call** - 调用PasswordCorrect块
2. **Else**
   - **Set Variable** - AttemptCount = AttemptCount + 1
   - **Call** - 调用PasswordWrong块

### Block 3: PasswordCorrect
**用途**: 密码正确时的处理
**Commands**:
1. **Set Variable** - PuzzleSolved = true
2. **Say** - "密码正确！门锁已解开。"
3. **Call Method** - 调用PasswordPuzzleUI.OnPasswordCorrect()
4. **Call Method** - 调用PasswordPuzzleTrigger.OnPuzzleSolved()

### Block 4: PasswordWrong
**用途**: 密码错误时的处理
**Commands**:
1. **If** - 条件: AttemptCount >= 3
   - **Say** - "尝试次数过多，系统锁定。"
   - **Call Method** - 调用PasswordPuzzleUI.OnPasswordWrong()
2. **Else**
   - **Say** - "密码错误，请重试。"
   - **Call Method** - 调用PasswordPuzzleUI.OnPasswordWrong()

## 3. UI设置

### 创建Canvas
1. 创建Canvas (Screen Space - Overlay)
2. 添加以下UI元素：

### 密码输入面板
```
Canvas
└── PasswordPanel (Panel)
    ├── Background (Image)
    ├── TitleText (Text) - "密码输入"
    ├── HintText (Text) - "请输入4位数字密码"
    ├── PasswordInput (InputField)
    │   ├── Placeholder (Text) - "输入密码..."
    │   └── Text (Text)
    ├── ButtonPanel (Panel)
    │   ├── ConfirmButton (Button)
    │   │   └── Text - "确认"
    │   └── CancelButton (Button)
    │       └── Text - "取消"
    └── StatusText (Text) - 显示状态信息
```

### 交互提示
```
Canvas
└── InteractionPrompt (Panel)
    └── PromptText (Text) - "按E键输入密码"
```

## 4. 组件配置

### PasswordPuzzleUI组件设置
- **Password Panel**: 拖入PasswordPanel
- **Password Input**: 拖入PasswordInput
- **Confirm Button**: 拖入ConfirmButton
- **Cancel Button**: 拖入CancelButton
- **Hint Text**: 拖入HintText
- **Status Text**: 拖入StatusText
- **Flowchart**: 拖入Flowchart组件
- **Check Password Block Name**: "CheckPassword"
- **Password Correct Block Name**: "PasswordCorrect"
- **Password Wrong Block Name**: "PasswordWrong"

### PasswordPuzzleTrigger组件设置
- **Puzzle Title**: "密码锁"
- **Hint Text**: "请输入4位数字密码"
- **Correct Password**: "1234"
- **Password UI**: 拖入PasswordPuzzleUI组件
- **Interaction Prompt**: 拖入InteractionPrompt
- **Flowchart**: 拖入Flowchart组件
- **Init Block Name**: "InitPasswordPuzzle"

## 5. 事件处理设置

### End Edit事件处理器
1. 在Flowchart上添加**End Edit**事件处理器
2. 设置Target Input Field为PasswordInput
3. 设置执行的Block为CheckPassword

### Button Clicked事件处理器
1. 为确认按钮添加**Button Clicked**事件处理器
2. 设置Target Button为ConfirmButton
3. 设置执行的Block为CheckPassword

## 6. 高级功能

### 动态密码生成
在InitPasswordPuzzle块中添加：
1. **Random Integer** - 生成随机数
2. **Concatenate** - 组合成密码字符串
3. **Set Variable** - 设置CorrectPassword

### 密码提示系统
创建额外的Block来提供密码提示：
1. **GetPasswordHint** - 根据游戏进度提供提示
2. **ShowHint** - 显示提示信息

### 多重密码验证
扩展CheckPassword块：
1. 添加多个密码变量
2. 使用**If**命令检查多个可能的正确答案

## 7. 测试清单

- [ ] 密码输入界面正常显示/隐藏
- [ ] 正确密码能够通过验证
- [ ] 错误密码显示相应提示
- [ ] 尝试次数限制正常工作
- [ ] 玩家移动在输入时被正确禁用/启用
- [ ] 回车键和确认按钮都能触发验证
- [ ] 取消按钮正常工作
- [ ] 谜题解决后状态正确更新

## 8. 常见问题

### Q: 变量没有正确传递
A: 确保在Flowchart中创建了所有必需的变量，并且变量名称与脚本中的完全匹配。

### Q: UI不显示
A: 检查Canvas的Render Mode和Camera设置，确保UI层级正确。

### Q: 按钮点击无响应
A: 确保Button组件的Interactable选项已勾选，并且EventSystem存在于场景中。

### Q: 密码验证不工作
A: 检查字符串比较是否区分大小写，考虑使用Trim()去除空格。
