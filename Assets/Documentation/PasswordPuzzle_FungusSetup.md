# 密码谜题 - Fungus设置指南（与InteractableItem集成）

## 概述
本指南详细说明如何使用Fungus插件创建与InteractableItem集成的密码输入谜题系统。

## 1. 系统架构

### 组件关系
```
InteractableItem (触发交互)
    ↓
Fungus Flowchart (对话和逻辑)
    ↓
PasswordPuzzleManager (密码管理)
    ↓
PasswordPuzzleUI (用户界面)
```

## 2. 创建Flowchart

1. 在场景中创建一个空的GameObject，命名为"PasswordPuzzleFlowchart"
2. 添加Flowchart组件
3. 添加PasswordPuzzleManager组件
4. 在Flowchart中创建以下变量：

### 必需变量
- **CorrectPassword** (String) - 存储正确密码
- **UserInput** (String) - 存储用户输入
- **PuzzleSolved** (Boolean) - 谜题解决状态
- **AttemptCount** (Integer) - 尝试次数计数
- **PasswordLength** (Integer) - 密码长度

## 3. 创建Block结构

### Block 1: PasswordPuzzleStart (InteractableItem触发)
**用途**: 玩家与物体交互时的初始对话
**Commands**:
1. **If** - 条件: PuzzleSolved == true
   - **Say** - "门已经解锁了。"
   - **Stop Flowchart**
2. **Else**
   - **Say** - "这是一个密码锁。"
   - **Say** - "需要输入{$PasswordLength}位数字密码才能解锁。"
   - **Call Method** - 调用PasswordPuzzleManager.ShowPasswordInput()

### Block 2: CheckPassword
**用途**: 检查用户输入的密码（由UI触发）
**Commands**:
1. **Call Method** - 调用PasswordPuzzleManager.CheckPassword()

### Block 3: PasswordCorrect
**用途**: 密码正确时的处理
**Commands**:
1. **Say** - "密码正确！门锁已解开。"
2. **Wait** - 等待1秒
3. **Call Method** - 调用GameManager.Instance.EnablePlayerMovement()

### Block 4: PasswordWrong
**用途**: 密码错误时的处理
**Commands**:
1. **If** - 条件: AttemptCount >= 3
   - **Say** - "尝试次数过多，系统暂时锁定。"
   - **Wait** - 等待2秒
   - **Call Method** - 调用GameManager.Instance.EnablePlayerMovement()
2. **Else**
   - **Say** - "密码错误！还有{$AttemptCount}次机会。"

## 4. InteractableItem设置

### 创建可交互物体
1. 创建一个GameObject（比如门、保险箱等）
2. 添加Collider2D组件，设置为Trigger
3. 添加InteractableItem组件
4. 配置InteractableItem：
   - **Item Name**: "密码锁"
   - **Flowchart**: 拖入PasswordPuzzleFlowchart
   - **Block Name**: "PasswordPuzzleStart"
   - **Interaction Key**: KeyCode.E
   - **Interaction Prompt**: 拖入交互提示UI

## 5. UI设置

### 创建Canvas
1. 创建Canvas (Screen Space - Overlay)
2. 添加以下UI元素：

### 密码输入面板
```
Canvas
└── PasswordPanel (Panel)
    ├── Background (Image)
    ├── TitleText (Text) - "密码输入"
    ├── HintText (Text) - "请输入密码"
    ├── PasswordInput (InputField)
    │   ├── Placeholder (Text) - "输入密码..."
    │   └── Text (Text)
    ├── ButtonPanel (Panel)
    │   ├── ConfirmButton (Button)
    │   │   └── Text - "确认"
    │   └── CancelButton (Button)
    │       └── Text - "取消"
    └── StatusText (Text) - 显示状态信息
```

## 6. 组件配置

### PasswordPuzzleManager组件设置
- **Puzzle Title**: "密码锁"
- **Correct Password**: "1234"
- **Password Length**: 4
- **Max Attempts**: 3
- **Password UI**: 拖入PasswordPuzzleUI组件
- **Flowchart**: 拖入Flowchart组件
- **Check Password Block Name**: "CheckPassword"
- **Password Correct Block Name**: "PasswordCorrect"
- **Password Wrong Block Name**: "PasswordWrong"

### PasswordPuzzleUI组件设置
- **Password Panel**: 拖入PasswordPanel
- **Password Input**: 拖入PasswordInput
- **Confirm Button**: 拖入ConfirmButton
- **Cancel Button**: 拖入CancelButton
- **Hint Text**: 拖入HintText
- **Status Text**: 拖入StatusText
- **Flowchart**: 拖入Flowchart组件
- **Check Password Block Name**: "CheckPassword"

## 7. 事件处理设置

### End Edit事件处理器（可选）
1. 在Flowchart上添加**End Edit**事件处理器
2. 设置Target Input Field为PasswordInput
3. 设置执行的Block为CheckPassword

### Button Clicked事件处理器（可选）
1. 为确认按钮添加**Button Clicked**事件处理器
2. 设置Target Button为ConfirmButton
3. 设置执行的Block为CheckPassword

**注意**: 由于PasswordPuzzleUI已经处理了按钮点击和回车事件，这些事件处理器是可选的。

## 8. 完整工作流程

1. **玩家接近可交互物体** → InteractableItem显示交互提示
2. **玩家按E键交互** → InteractableItem触发Fungus Block "PasswordPuzzleStart"
3. **Fungus显示对话** → 告诉玩家需要输入密码
4. **调用ShowPasswordInput()** → PasswordPuzzleManager显示密码输入界面
5. **玩家输入密码** → PasswordPuzzleUI处理用户输入
6. **点击确认或按回车** → 触发CheckPassword Block
7. **密码验证** → PasswordPuzzleManager检查密码
8. **结果处理** → 根据结果执行相应的Block

## 9. 高级功能

### 动态密码生成
在PasswordPuzzleStart块中添加：
1. **Random Integer** - 生成随机数
2. **Concatenate** - 组合成密码字符串
3. **Set Variable** - 设置CorrectPassword
4. **Call Method** - 调用PasswordPuzzleManager.SetPassword()

### 密码提示系统
创建额外的Block来提供密码提示：
1. **GetPasswordHint** - 根据游戏进度提供提示
2. **ShowHint** - 显示提示信息
3. **Menu** - 提供"需要提示吗？"选项

### 多重密码验证
扩展CheckPassword逻辑：
1. 添加多个密码变量
2. 使用**If**命令检查多个可能的正确答案
3. 支持不同难度级别的密码

## 10. 测试清单

- [ ] InteractableItem正常触发对话
- [ ] 对话正确显示密码要求
- [ ] 密码输入界面正常显示/隐藏
- [ ] 正确密码能够通过验证
- [ ] 错误密码显示相应提示
- [ ] 尝试次数限制正常工作
- [ ] 玩家移动在输入时被正确禁用/启用
- [ ] 回车键和确认按钮都能触发验证
- [ ] 取消按钮正常工作
- [ ] 谜题解决后状态正确更新
- [ ] 再次交互时显示"已解锁"信息

## 11. 常见问题

### Q: InteractableItem不触发
A: 检查Collider2D是否设置为Trigger，玩家是否有正确的Tag。

### Q: 变量没有正确传递
A: 确保在Flowchart中创建了所有必需的变量，并且变量名称与脚本中的完全匹配。

### Q: UI不显示
A: 检查Canvas的Render Mode和Camera设置，确保UI层级正确。

### Q: 密码验证不工作
A: 检查PasswordPuzzleManager是否正确连接到Flowchart，变量是否正确设置。

### Q: 对话后UI不显示
A: 确保在对话Block中正确调用了PasswordPuzzleManager.ShowPasswordInput()方法。
