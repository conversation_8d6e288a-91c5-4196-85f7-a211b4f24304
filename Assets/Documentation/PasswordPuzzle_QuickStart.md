# 密码谜题快速开始指南

## 概述
本指南将帮助你快速设置一个与InteractableItem集成的密码谜题系统。

## 快速设置步骤

### 1. 创建基础GameObject
```
1. 创建空GameObject，命名为"PasswordPuzzle"
2. 添加Collider2D组件，设置为Trigger
3. 添加以下组件：
   - InteractableItem
   - Flowchart
   - PasswordPuzzleManager
   - PasswordPuzzleExample（可选，用于测试）
```

### 2. 设置InteractableItem
```
- Item Name: "密码锁"
- Flowchart: 拖入同一GameObject上的Flowchart
- Block Name: "PasswordPuzzleStart"
- Interaction Key: KeyCode.E
- Interaction Prompt: 拖入交互提示UI
```

### 3. 创建Fungus变量
在Flowchart中创建以下变量：
```
- CorrectPassword (String): "1234"
- UserInput (String): ""
- PuzzleSolved (Boolean): false
- AttemptCount (Integer): 0
- PasswordLength (Integer): 4
```

### 4. 创建Fungus Blocks

#### Block: PasswordPuzzleStart
```
Commands:
1. If (PuzzleSolved == true)
   - Say: "门已经解锁了。"
   - Stop Flowchart
2. Else
   - Say: "这是一个密码锁。"
   - Say: "需要输入{$PasswordLength}位数字密码才能解锁。"
   - Call Method: PasswordPuzzleManager.ShowPasswordInput
```

#### Block: CheckPassword
```
Commands:
1. Call Method: PasswordPuzzleManager.CheckPassword
```

#### Block: PasswordCorrect
```
Commands:
1. Say: "密码正确！门锁已解开。"
2. Wait: 1秒
3. Call Method: GameManager.Instance.EnablePlayerMovement
```

#### Block: PasswordWrong
```
Commands:
1. If (AttemptCount >= 3)
   - Say: "尝试次数过多，系统暂时锁定。"
   - Wait: 2秒
   - Call Method: GameManager.Instance.EnablePlayerMovement
2. Else
   - Say: "密码错误！还有机会重试。"
```

### 5. 设置UI界面

#### 创建Canvas
```
Canvas (Screen Space - Overlay)
└── PasswordPanel (Panel)
    ├── TitleText (Text): "密码输入"
    ├── HintText (Text): "请输入密码"
    ├── PasswordInput (InputField 或 TMP_InputField)
    ├── ConfirmButton (Button): "确认"
    ├── CancelButton (Button): "取消"
    └── StatusText (Text): 状态信息
```

#### 创建InputField（重要）
**选择其中一种方式**：

**方式1 - Legacy InputField**：
```
右键点击Canvas → UI → Legacy → Input Field
```

**方式2 - TextMeshPro InputField**：
```
1. Window → TextMeshPro → Import TMP Essential Resources
2. 右键点击Canvas → UI → Input Field - TextMeshPro
```

#### 添加PasswordPuzzleUI组件
```
将PasswordPuzzleUI组件添加到Canvas上，并设置：
- Password Panel: PasswordPanel
- Legacy Password Input: 拖入Legacy InputField（如果使用）
- Tmp Password Input: 拖入TMP InputField（如果使用）
- Confirm Button: ConfirmButton
- Cancel Button: CancelButton
- Hint Text: HintText
- Status Text: StatusText
- Flowchart: 拖入Flowchart
```

**注意**：只设置其中一种InputField，另一个保持为空。

### 6. 配置PasswordPuzzleManager
```
- Puzzle Title: "密码锁"
- Correct Password: "1234"
- Password Length: 4
- Max Attempts: 3
- Password UI: 拖入PasswordPuzzleUI
- Flowchart: 拖入Flowchart
```

## 工作流程

1. **玩家接近** → InteractableItem显示"按E键交互"
2. **按E键** → 触发PasswordPuzzleStart Block
3. **显示对话** → "这是一个密码锁，需要输入4位数字密码"
4. **显示UI** → 密码输入界面出现
5. **输入密码** → 玩家在InputField中输入
6. **验证密码** → 点击确认或按回车触发验证
7. **显示结果** → 根据验证结果显示相应对话

## 测试方法

### 使用PasswordPuzzleExample组件
```
1. 添加PasswordPuzzleExample组件到谜题GameObject
2. 在Inspector中点击"设置示例谜题"
3. 点击"测试密码验证"验证功能
4. 点击"显示密码输入"测试UI
```

### 手动测试
```
1. 运行游戏
2. 控制玩家角色接近谜题物体
3. 按E键触发交互
4. 在密码输入框中输入"1234"
5. 点击确认或按回车
6. 观察是否显示"密码正确"对话
```

## 自定义选项

### 修改密码
```csharp
// 在代码中动态修改
passwordPuzzleManager.SetPassword("5678");

// 或在Inspector中直接修改
// PasswordPuzzleManager -> Correct Password
```

### 修改尝试次数
```csharp
// 在PasswordPuzzleManager中修改Max Attempts
```

### 添加密码提示
```
在PasswordPuzzleStart Block中添加：
- Menu: "需要提示吗？"
  - Option 1: "是" -> Say: "密码是当前年份的后四位"
  - Option 2: "否" -> Continue
```

### 动态生成密码
```
在PasswordPuzzleStart Block开始时添加：
1. Random Integer (1000-9999) -> 存储到CorrectPassword
2. Call Method: PasswordPuzzleManager.SetPassword
```

## 常见问题解决

### Q: 点击E键没有反应
```
检查：
- InteractableItem的Flowchart是否正确设置
- Block Name是否为"PasswordPuzzleStart"
- Collider2D是否设置为Trigger
- 玩家GameObject是否有"Player" Tag
```

### Q: InputField无法拖入PasswordPuzzleUI
```
原因：InputField类型不匹配
解决：
- Legacy InputField → 拖入 "Legacy Password Input" 字段
- TMP InputField → 拖入 "Tmp Password Input" 字段
- 只设置其中一种，另一个保持为空
- 参考 InputField_Setup_Guide.md 详细说明
```

### Q: 密码输入界面不显示
```
检查：
- PasswordPuzzleManager是否正确连接PasswordPuzzleUI
- Canvas是否激活
- PasswordPanel是否正确设置
- InputField是否正确设置（Legacy或TMP）
```

### Q: 密码验证不工作
```
检查：
- Fungus变量名称是否正确
- PasswordPuzzleManager的Flowchart引用是否正确
- CheckPassword Block是否存在
```

## 扩展功能

### 多级密码系统
- 创建多个PasswordPuzzleManager
- 使用不同的Flowchart变量
- 根据游戏进度解锁不同密码

### 密码复杂度
- 支持字母+数字组合
- 大小写敏感选项
- 特殊字符支持

### 视觉反馈
- 密码输入时的字符遮罩
- 错误时的屏幕震动效果
- 成功时的粒子特效
