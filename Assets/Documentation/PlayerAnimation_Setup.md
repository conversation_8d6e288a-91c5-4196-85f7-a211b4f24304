# 玩家动画设置指南

## 概述
修改后的PlayerController现在支持在移动时播放动画，包括行走动画、待机动画和精灵翻转功能。

## 组件要求

### 必需组件
1. **Animator** - 用于播放动画
2. **SpriteRenderer** - 用于精灵翻转（可选）

### 推荐的GameObject结构
```
Player
├── PlayerController (Script)
├── Animator
├── SpriteRenderer
└── Collider2D
```

## 动画设置步骤

### 1. 创建动画控制器
1. 在Project窗口中右键 → Create → Animator Controller
2. 命名为"PlayerAnimatorController"
3. 将其拖入Player的Animator组件的Controller字段

### 2. 创建动画状态
在Animator窗口中创建以下状态：
- **Idle** - 待机动画
- **Walk** - 行走动画

### 3. 设置动画片段
1. 选中Idle状态，在Inspector中设置Motion为待机动画片段
2. 选中Walk状态，在Inspector中设置Motion为行走动画片段

### 4. 配置PlayerController组件

#### Inspector设置
```
移动设置:
- Move Speed: 5 (移动速度)
- Left Boundary: -10 (左边界)
- Right Boundary: 10 (右边界)

动画设置:
- Animator: 拖入Animator组件
- Walk Animation Name: "Walk" (行走动画状态名称)
- Idle Animation Name: "Idle" (待机动画状态名称)
- Flip Sprite On Direction: ✓ (是否根据移动方向翻转精灵)

状态:
- Has Key: false (是否有钥匙)
```

## 动画状态名称

### 默认状态名称
- **Idle** - 待机动画
- **Walk** - 行走动画

### 自定义状态名称
如果你的动画状态有不同的名称，请在Inspector中修改：
- Walk Animation Name: 你的行走动画状态名称
- Idle Animation Name: 你的待机动画状态名称

## 功能特性

### 1. 自动动画切换
- **移动时** → 播放行走动画
- **停止移动** → 播放待机动画
- **禁用移动** → 强制播放待机动画

### 2. 精灵翻转
- **向右移动** → 精灵不翻转
- **向左移动** → 精灵水平翻转
- **可选功能** → 通过Flip Sprite On Direction控制

### 3. 边界检测
- 玩家不会移动到设定的边界之外
- 到达边界时停止移动但不影响动画

## 代码API

### 公共方法
```csharp
// 移动控制
EnableMovement()        // 启用移动
DisableMovement()       // 禁用移动

// 动画控制
PlayAnimation(string)   // 播放指定动画
IsMoving()             // 检查是否正在移动

// 速度控制
SetMoveSpeed(float)    // 设置移动速度
GetMoveSpeed()         // 获取当前移动速度

// 游戏逻辑
GetKey()               // 获得钥匙
```

## 常见问题

### Q: 动画不播放
**检查项目**:
- Animator组件是否正确设置
- Animator Controller是否已分配
- 动画状态名称是否与代码中的匹配
- 动画片段是否正确分配给状态

### Q: 精灵不翻转
**检查项目**:
- Flip Sprite On Direction是否勾选
- SpriteRenderer组件是否存在
- 精灵的Pivot是否设置为Center

### Q: 移动不流畅
**调整参数**:
- 降低Move Speed值
- 检查动画的帧率和循环设置
- 确保动画片段的Loop Time已勾选

### Q: 边界检测不工作
**检查设置**:
- Left Boundary和Right Boundary值是否正确
- 确保Left Boundary < Right Boundary

## 扩展功能

### 添加更多动画
1. 在Animator Controller中添加新状态
2. 在PlayerController中添加对应的方法：

```csharp
public void PlayJumpAnimation()
{
    PlayAnimation("Jump");
}

public void PlayAttackAnimation()
{
    PlayAnimation("Attack");
}
```

### 使用Animator参数
如果你想使用Animator参数而不是直接播放状态：

```csharp
// 在SetMovingState方法中使用参数
animator.SetBool("IsWalking", moving);
```

### 添加音效
在SetMovingState方法中添加音效播放：

```csharp
if (isMoving && !wasMoving)
{
    // 开始移动时播放脚步声
    AudioSource.PlayClipAtPoint(footstepSound, transform.position);
}
```

## 测试清单

- [ ] 玩家可以左右移动
- [ ] 移动时播放行走动画
- [ ] 停止时播放待机动画
- [ ] 精灵根据移动方向正确翻转
- [ ] 边界检测正常工作
- [ ] 禁用移动时强制播放待机动画
- [ ] 没有控制台错误信息
