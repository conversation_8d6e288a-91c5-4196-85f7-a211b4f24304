Fungus
======

The goal of Fungus is to provide a free, open source tool for creating interactive storytelling games in Unity 3D. 
Fungus is designed to be easy to learn for beginners to Unity 3D, especially for people with no coding experience. 

For power users, it provides an intuitive, fast workflow for visual scripting and interactive storytelling. 
Fungus is being used to create Visual Novels, Point and Click Adventure Games, Childrens Stories, Hidden Object Games, 
eLearning apps and also some frankly weird stuff which defies classification :)

- Author: <PERSON>
- Website: fungusgames.com
- Email: <EMAIL>
- Twitter: @gofungus
- Facebook: facebook.com/fungusgames

Instructions
============

Installation instructions and tutorial videos are available on the official Fungus website.
http://fungusgames.com/learn

Support
=======

If you have questions about Fungus, please search our forum first as someone may have had the same issue already. If you can't find an answer please start a new discussion and we'll answer you as soon as we can. Fungus is designed for beginners and we love to hear from users so please don't be shy about posting!
http://fungusgames.com/forum

Contributing
============

Many thanks to everyone who has contributed code to the project.
https://github.com/snozbot/fungus/graphs/contributors

If you would like to contribute a bug fix or new feature, please submit a pull request on github (https://github.com/snozbot/fungus)

And if you are interested in contributing in some other way (art, audio, documentation, pizza) just email <NAME_EMAIL>.
