%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &142980
GameObject:
  m_ObjectHideFlags: 0
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  serializedVersion: 4
  m_Component:
  - 4: {fileID: 467082}
  - 114: {fileID: 11430050}
  - 114: {fileID: 11433304}
  - 114: {fileID: 11462346}
  m_Layer: 0
  m_Name: Flowchart
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &467082
Transform:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 142980}
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
  m_Children: []
  m_Father: {fileID: 0}
  m_RootOrder: 0
--- !u!114 &11430050
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 142980}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a334fe2ffb574b3583ff3b18b4792d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 1
  scrollPos: {x: 0, y: 0}
  variablesScrollPos: {x: 0, y: 0}
  variablesExpanded: 1
  blockViewHeight: 400
  zoom: 1
  scrollViewRect:
    serializedVersion: 2
    x: -343
    y: -340
    width: 1114
    height: 859
  selectedBlock: {fileID: 11433304}
  selectedCommands: []
  variables: []
  description: 
  stepPause: 0
  colorCommands: 1
  hideComponents: 1
  saveSelection: 1
  localizationId: 
  showLineNumbers: 0
  hideCommands: []
  luaEnvironment: {fileID: 0}
  luaBindingName: flowchart
--- !u!114 &11433304
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 142980}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 67
    y: 70
    width: 120
    height: 40
  itemId: 0
  blockName: New Block
  description: 
  eventHandler: {fileID: 11462346}
  commandList: []
--- !u!114 &11462346
MonoBehaviour:
  m_ObjectHideFlags: 2
  m_PrefabParentObject: {fileID: 0}
  m_PrefabInternal: {fileID: 100100000}
  m_GameObject: {fileID: 142980}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d2f6487d21a03404cb21b245f0242e79, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  parentBlock: {fileID: 11433304}
--- !u!1001 &100100000
Prefab:
  m_ObjectHideFlags: 1
  serializedVersion: 2
  m_Modification:
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 0}
      propertyPath: commandList.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 0}
      propertyPath: selectedCommands.Array.size
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 0}
      propertyPath: hideComponents
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 0}
      propertyPath: selectedBlock
      value: 
      objectReference: {fileID: 11433304}
    - target: {fileID: 0}
      propertyPath: commandList.Array.data[0]
      value: 
      objectReference: {fileID: 0}
    - target: {fileID: 0}
      propertyPath: selectedCommands.Array.data[0]
      value: 
      objectReference: {fileID: 0}
    m_RemovedComponents: []
  m_ParentPrefab: {fileID: 0}
  m_RootGameObject: {fileID: 142980}
  m_IsPrefabParent: 1
