// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// If the test expression is true, execute the following command block.
    /// </summary>
	[CommandInfo("Flow", 
	             "Lua If", 
	             "If the test expression is true, execute the following command block.")]
    [AddComponentMenu("")]
    public class LuaIf : LuaCondition
    {
        #region Public members

        #endregion
    }
}