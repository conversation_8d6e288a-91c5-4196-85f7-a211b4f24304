// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/
using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// Get or Set a property of a Character component
    /// </summary>
    [CommandInfo("Property",
                 "Character",
                 "Get or Set a property of a Character component")]
    [AddComponentMenu("")]
    public class CharacterProperty : BaseVariableProperty
    {
		//generated property
        public enum Property 
        { 
            NameText, 
            NameColor, 
            SoundEffect, 
            ProfileSprite, 
            VoiceAudioSource, 
            EffectAudioSource, 
            SayDialogGameObject, 
        }


        [SerializeField]
        protected Property property;

        [SerializeField]
        protected CharacterData characterData;

        [SerializeField]
        [VariableProperty(typeof(SpriteVariable),
                          typeof(StringVariable),
                          typeof(ColorVariable),
                          typeof(AudioClipVariable),
                          typeof(AudioSourceVariable),
                          typeof(GameObjectVariable))]
        protected Variable inOutVar;

        public override void OnEnter()
        {
            var iospr = inOutVar as SpriteVariable;
            var ios = inOutVar as StringVariable;
            var iocol = inOutVar as ColorVariable;
            var ioac = inOutVar as AudioClipVariable;
            var ioaud = inOutVar as AudioSourceVariable;
            var iogo = inOutVar as GameObjectVariable;


            var target = characterData.Value;

            switch (getOrSet)
            {
                case GetSet.Get:
                    switch (property)
                    {
                        case Property.ProfileSprite:
                            iospr.Value = target.ProfileSprite;
                            break;
                        case Property.NameText:
                            ios.Value = target.NameText;
                            break;
                        case Property.NameColor:
                            iocol.Value = target.NameColor;
                            break;
                        case Property.SoundEffect:
                            ioac.Value = target.SoundEffect;
                            break;
                        case Property.VoiceAudioSource:
                            ioaud.Value = target.VoiceAudioSource;
                            break;
                        case Property.EffectAudioSource:
                            ioaud.Value = target.EffectAudioSource;
                            break;
                        case Property.SayDialogGameObject:
                            iogo.Value = target.SayDialogGameObject;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;

                case GetSet.Set:
                    switch (property)
                    {
                        case Property.ProfileSprite:
                            target.ProfileSprite = iospr.Value;
                            break;
                        case Property.NameColor:
                            target.NameColor = iocol.Value;
                            break;
                        case Property.SoundEffect:
                            target.SoundEffect = ioac.Value;
                            break;
                        case Property.VoiceAudioSource:
                            target.VoiceAudioSource = ioaud.Value;
                            break;
                        case Property.EffectAudioSource:
                            target.EffectAudioSource = ioaud.Value;
                            break;
                        case Property.SayDialogGameObject:
                            target.SayDialogGameObject = iogo.Value;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;

                default:
                    break;
            }

            Continue();
        }

        public override string GetSummary()
        {
            if (characterData.Value == null)
            {
                return "Error: no character set";
            }
            if (inOutVar == null)
            {
                return "Error: no variable set to push or pull data to or from";
            }

            return getOrSet.ToString() + " " + property.ToString();
        }

        public override Color GetButtonColor()
        {
            return new Color32(235, 191, 217, 255);
        }

        public override bool HasReference(Variable variable)
        {
            if (characterData.characterRef == variable || inOutVar == variable)
                return true;

            return false;
        }
    }
}