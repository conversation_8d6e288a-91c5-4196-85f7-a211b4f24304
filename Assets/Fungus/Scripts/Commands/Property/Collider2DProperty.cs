// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/
using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// Get or Set a property of a Collider2D component
    /// </summary>
    [CommandInfo("Property",
                 "Collider2D",
                 "Get or Set a property of a Collider2D component")]
    [AddComponentMenu("")]
    public class Collider2DProperty : BaseVariableProperty
    {
		//generated property
        public enum Property 
        { 
            Density, 
            IsTrigger, 
            UsedByEffector, 
            UsedByComposite, 
            Offset, 
            AttachedRigidbody, 
            ShapeCount, 
            Friction, 
            Bounciness, 
        }


        [SerializeField]
        protected Property property;

        [SerializeField]
        protected Collider2DData collider2DData;

        [SerializeField]
        [VariableProperty(typeof(FloatVariable),
                          typeof(BooleanVariable),
                          typeof(Vector2Variable),
                          typeof(Rigidbody2DVariable),
                          typeof(IntegerVariable))]
        protected Variable inOutVar;

        public override void OnEnter()
        {
            var iof = inOutVar as FloatVariable;
            var iob = inOutVar as BooleanVariable;
            var iov2 = inOutVar as Vector2Variable;
            var iorb2d = inOutVar as Rigidbody2DVariable;
            var ioi = inOutVar as IntegerVariable;


            var target = collider2DData.Value;

            switch (getOrSet)
            {
                case GetSet.Get:
                    switch (property)
                    {
                        case Property.Density:
                            iof.Value = target.density;
                            break;
                        case Property.IsTrigger:
                            iob.Value = target.isTrigger;
                            break;
                        case Property.UsedByEffector:
                            iob.Value = target.usedByEffector;
                            break;
                        case Property.UsedByComposite:

                        // #if UNITY_6000
                        //     iob.Value = target.compositeOperation != Collider2D.CompositeOperation.None;
                        // #else
                            iob.Value = target.usedByComposite;
                        // #endif

                            break;
                        case Property.Offset:
                            iov2.Value = target.offset;
                            break;
                        case Property.AttachedRigidbody:
                            iorb2d.Value = target.attachedRigidbody;
                            break;
                        case Property.ShapeCount:
                            ioi.Value = target.shapeCount;
                            break;
                        case Property.Friction:
                            iof.Value = target.friction;
                            break;
                        case Property.Bounciness:
                            iof.Value = target.bounciness;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;

                case GetSet.Set:
                    switch (property)
                    {
                        case Property.Density:
                            target.density = iof.Value;
                            break;
                        case Property.IsTrigger:
                            target.isTrigger = iob.Value;
                            break;
                        case Property.UsedByEffector:
                            target.usedByEffector = iob.Value;
                            break;
                        case Property.UsedByComposite:

                        // #if UNITY_6000
                        //     target.compositeOperation = iob.Value;
                        // #else
                            target.usedByComposite = iob.Value;
                        // #endif

                            break;
                        case Property.Offset:
                            target.offset = iov2.Value;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;

                default:
                    break;
            }

            Continue();
        }

        public override string GetSummary()
        {
            if (collider2DData.Value == null)
            {
                return "Error: no collider2D set";
            }
            if (inOutVar == null)
            {
                return "Error: no variable set to push or pull data to or from";
            }

            return getOrSet.ToString() + " " + property.ToString();
        }

        public override Color GetButtonColor()
        {
            return new Color32(235, 191, 217, 255);
        }

        public override bool HasReference(Variable variable)
        {
            if (collider2DData.collider2DRef == variable || inOutVar == variable)
                return true;

            return false;
        }
    }
}