/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/
using UnityEngine;


namespace Fungus
{
    /// <summary>
    /// Get or Set a property of a Collision component
    /// </summary>
    [CommandInfo("Property",
                 "Collision",
                 "Get or Set a property of a Collision component")]
    [AddComponentMenu("")]
    public class CollisionProperty : BaseVariableProperty
    {
		//generated property
        public enum Property 
        { 
            RelativeVelocity, 
            Rigidbody, 
            Collider, 
            Transform, 
            GameObject, 
            Impulse, 
        }

		
        [SerializeField]
        protected Property property;
		
        [SerializeField]
        [VariableProperty(typeof(CollisionVariable))]
        protected CollisionVariable collisionVar;

        [SerializeField]
        [VariableProperty(typeof(Vector3Variable),
                          typeof(RigidbodyVariable),
                          typeof(ColliderVariable),
                          typeof(TransformVariable),
                          typeof(GameObjectVariable))]
        protected Variable inOutVar;

        public override void OnEnter()
        {
            var iov = inOutVar as Vector3Variable;
            var iorb = inOutVar as RigidbodyVariable;
            var ioc = inOutVar as ColliderVariable;
            var iot = inOutVar as TransformVariable;
            var iogo = inOutVar as GameObjectVariable;


            var target = collisionVar.Value;

            switch (getOrSet)
            {
                case GetSet.Get:
                    switch (property)
                    {
                        case Property.RelativeVelocity:
                            iov.Value = target.relativeVelocity;
                            break;
                        case Property.Rigidbody:
                            iorb.Value = target.rigidbody;
                            break;
                        case Property.Collider:
                            ioc.Value = target.collider;
                            break;
                        case Property.Transform:
                            iot.Value = target.transform;
                            break;
                        case Property.GameObject:
                            iogo.Value = target.gameObject;
                            break;
                        case Property.Impulse:
                            iov.Value = target.impulse;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;
                case GetSet.Set:
                    switch (property)
                    {
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;
                default:
                    break;
            }

            Continue();
        }

        public override string GetSummary()
        {
            if (collisionVar == null)
            {
                return "Error: no collisionVar set";
            }
            if (inOutVar == null)
            {
                return "Error: no variable set to push or pull data to or from";
            }

            return getOrSet.ToString() + " " + property.ToString();
        }

        public override Color GetButtonColor()
        {
            return new Color32(235, 191, 217, 255);
        }

        public override bool HasReference(Variable variable)
        {
            if (collisionVar == variable || inOutVar == variable)
                return true;

            return false;
        }

    }
}