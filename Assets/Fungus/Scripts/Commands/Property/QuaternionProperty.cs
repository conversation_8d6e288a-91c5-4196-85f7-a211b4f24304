// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/
using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// Get or Set a property of a Quaternion component
    /// </summary>
    [CommandInfo("Property",
                 "Quaternion",
                 "Get or Set a property of a Quaternion component")]
    [AddComponentMenu("")]
    public class QuaternionProperty : BaseVariableProperty
    {
		//generated property
        public enum Property 
        { 
            X, 
            Y, 
            Z, 
            W, 
            EulerAngles, 
            Normalized, 
        }

		
        [SerializeField]
        protected Property property;
		
        [SerializeField]
        protected QuaternionData quaternionData;

        [SerializeField]
        [VariableProperty(typeof(FloatVariable),
                          typeof(Vector3Variable),
                          typeof(QuaternionVariable))]
        protected Variable inOutVar;

        public override void OnEnter()
        {
            var iof = inOutVar as FloatVariable;
            var iov = inOutVar as Vector3Variable;
#if UNITY_2019_2_OR_NEWER
            var ioq = inOutVar as QuaternionVariable;
#endif


            var target = quaternionData.Value;

            switch (getOrSet)
            {
                case GetSet.Get:
                    switch (property)
                    {
                        case Property.X:
                            iof.Value = target.x;
                            break;
                        case Property.Y:
                            iof.Value = target.y;
                            break;
                        case Property.Z:
                            iof.Value = target.z;
                            break;
                        case Property.W:
                            iof.Value = target.w;
                            break;
                        case Property.EulerAngles:
                            iov.Value = target.eulerAngles;
                            break;
#if UNITY_2019_2_OR_NEWER
                        case Property.Normalized:
                            ioq.Value = target.normalized;
                            break;
#endif
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;
                case GetSet.Set:
                    switch (property)
                    {
                        case Property.X:
                            target.x = iof.Value;
                            break;
                        case Property.Y:
                            target.y = iof.Value;
                            break;
                        case Property.Z:
                            target.z = iof.Value;
                            break;
                        case Property.W:
                            target.w = iof.Value;
                            break;
                        case Property.EulerAngles:
                            target.eulerAngles = iov.Value;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;
                default:
                    break;
            }

            quaternionData.Value = target;

            Continue();
        }

        public override string GetSummary()
        {
            if (inOutVar == null)
            {
                return "Error: no variable set to push or pull data to or from";
            }

            return getOrSet.ToString() + " " + property.ToString();
        }

        public override Color GetButtonColor()
        {
            return new Color32(235, 191, 217, 255);
        }

        public override bool HasReference(Variable variable)
        {
            if (quaternionData.quaternionRef == variable || inOutVar == variable)
                return true;

            return false;
        }

    }
}