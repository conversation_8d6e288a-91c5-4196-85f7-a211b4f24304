// This code is part of the Fungus library (https://github.com/snozbot/fungus)
// It is released for free under the MIT open source license (https://github.com/snozbot/fungus/blob/master/LICENSE)

/*This script has been, partially or completely, generated by the Fungus.GenerateVariableWindow*/
using UnityEngine;

namespace Fungus
{
    /// <summary>
    /// Get or Set a property of a Rigidbody component
    /// </summary>
    [CommandInfo("Property",
                 "Rigidbody",
                 "Get or Set a property of a Rigidbody component")]
    [AddComponentMenu("")]
    public class RigidbodyProperty : BaseVariableProperty
    {
		//generated property
        public enum Property 
        { 
            Velocity, 
            AngularVelocity, 
            Drag, 
            AngularDrag, 
            Mass, 
            UseGravity, 
            MaxDepenetrationVelocity, 
            IsKinematic, 
            FreezeRotation, 
            CenterOfMass, 
            WorldCenterOfMass, 
            InertiaTensorRotation, 
            InertiaTensor, 
            DetectCollisions, 
            Position, 
            Rotation, 
            SolverIterations, 
            SolverVelocityIterations, 
            SleepThreshold, 
            MaxAngularVelocity, 
        }


        [SerializeField]
        protected Property property;

        [SerializeField]
        protected RigidbodyData rigidbodyData;

        [SerializeField]
        [VariableProperty(typeof(Vector3Variable),
                          typeof(FloatVariable),
                          typeof(BooleanVariable),
                          typeof(QuaternionVariable),
                          typeof(IntegerVariable))]
        protected Variable inOutVar;

        public override void OnEnter()
        {
            var iov = inOutVar as Vector3Variable;
            var iof = inOutVar as FloatVariable;
            var iob = inOutVar as BooleanVariable;
            var ioq = inOutVar as QuaternionVariable;
            var ioi = inOutVar as IntegerVariable;


            var target = rigidbodyData.Value;

            switch (getOrSet)
            {
                case GetSet.Get:
                    switch (property)
                    {
                        case Property.Velocity:
                            #if UNITY_6000
                            iov.Value = target.linearVelocity;
                            #else
                            iov.Value = target.velocity;
                            #endif
                            break;
                        case Property.AngularVelocity:
                            iov.Value = target.angularVelocity;
                            break;
                        case Property.Drag:
                            #if UNITY_6000
                            iof.Value = target.linearDamping;
                            #else
                            iof.Value = target.drag;
                            #endif
                            break;
                        case Property.AngularDrag:
                            #if UNITY_6000
                            iof.Value = target.angularDamping;
                            #else
                            iof.Value = target.angularDrag;
                            #endif
                            break;
                        case Property.Mass:
                            iof.Value = target.mass;
                            break;
                        case Property.UseGravity:
                            iob.Value = target.useGravity;
                            break;
                        case Property.MaxDepenetrationVelocity:
                            iof.Value = target.maxDepenetrationVelocity;
                            break;
                        case Property.IsKinematic:
                            iob.Value = target.isKinematic;
                            break;
                        case Property.FreezeRotation:
                            iob.Value = target.freezeRotation;
                            break;
                        case Property.CenterOfMass:
                            iov.Value = target.centerOfMass;
                            break;
                        case Property.WorldCenterOfMass:
                            iov.Value = target.worldCenterOfMass;
                            break;
                        case Property.InertiaTensorRotation:
                            ioq.Value = target.inertiaTensorRotation;
                            break;
                        case Property.InertiaTensor:
                            iov.Value = target.inertiaTensor;
                            break;
                        case Property.DetectCollisions:
                            iob.Value = target.detectCollisions;
                            break;
                        case Property.Position:
                            iov.Value = target.position;
                            break;
                        case Property.Rotation:
                            ioq.Value = target.rotation;
                            break;
                        case Property.SolverIterations:
                            ioi.Value = target.solverIterations;
                            break;
                        case Property.SleepThreshold:
                            iof.Value = target.sleepThreshold;
                            break;
                        case Property.MaxAngularVelocity:
                            iof.Value = target.maxAngularVelocity;
                            break;
                        case Property.SolverVelocityIterations:
                            ioi.Value = target.solverVelocityIterations;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;

                case GetSet.Set:
                    switch (property)
                    {
                        case Property.Velocity:
                            #if UNITY_6000
                            target.linearVelocity = iov.Value;
                            #else
                            target.velocity = iov.Value;
                            #endif
                            break;
                        case Property.AngularVelocity:
                            target.angularVelocity = iov.Value;
                            break;
                        case Property.Drag:
                            #if UNITY_6000
                            target.linearDamping = iof.Value;
                            #else
                            target.drag = iof.Value;
                            #endif
                            break;
                        case Property.AngularDrag:
                            #if UNITY_6000
                            target.angularDamping = iof.Value;
                            #else
                            target.angularDrag = iof.Value;
                            #endif
                            break;
                        case Property.Mass:
                            target.mass = iof.Value;
                            break;
                        case Property.UseGravity:
                            target.useGravity = iob.Value;
                            break;
                        case Property.MaxDepenetrationVelocity:
                            target.maxDepenetrationVelocity = iof.Value;
                            break;
                        case Property.IsKinematic:
                            target.isKinematic = iob.Value;
                            break;
                        case Property.FreezeRotation:
                            target.freezeRotation = iob.Value;
                            break;
                        case Property.CenterOfMass:
                            target.centerOfMass = iov.Value;
                            break;
                        case Property.InertiaTensorRotation:
                            target.inertiaTensorRotation = ioq.Value;
                            break;
                        case Property.InertiaTensor:
                            target.inertiaTensor = iov.Value;
                            break;
                        case Property.DetectCollisions:
                            target.detectCollisions = iob.Value;
                            break;
                        case Property.Position:
                            target.position = iov.Value;
                            break;
                        case Property.Rotation:
                            target.rotation = ioq.Value;
                            break;
                        case Property.SolverIterations:
                            target.solverIterations = ioi.Value;
                            break;
                        case Property.SleepThreshold:
                            target.sleepThreshold = iof.Value;
                            break;
                        case Property.MaxAngularVelocity:
                            target.maxAngularVelocity = iof.Value;
                            break;
                        case Property.SolverVelocityIterations:
                            target.solverVelocityIterations = ioi.Value;
                            break;
                        default:
                            Debug.Log("Unsupported get or set attempted");
                            break;
                    }

                    break;

                default:
                    break;
            }

            Continue();
        }

        public override string GetSummary()
        {
            if (rigidbodyData.Value == null)
            {
                return "Error: no rigidbody set";
            }
            if (inOutVar == null)
            {
                return "Error: no variable set to push or pull data to or from";
            }

            return getOrSet.ToString() + " " + property.ToString();
        }

        public override Color GetButtonColor()
        {
            return new Color32(235, 191, 217, 255);
        }

        public override bool HasReference(Variable variable)
        {
            if (rigidbodyData.rigidbodyRef == variable || inOutVar == variable)
                return true;

            return false;
        }
    }
}