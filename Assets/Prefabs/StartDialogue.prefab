%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &1890220114624908046
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3321059571650704831}
  - component: {fileID: 8040565083048671696}
  - component: {fileID: 4087058147526112350}
  - component: {fileID: 7090517491754809714}
  - component: {fileID: 599364693722615891}
  - component: {fileID: 4192772015813394289}
  - component: {fileID: 2371872450066642155}
  - component: {fileID: 8184919673541066407}
  - component: {fileID: 3184283698437494502}
  - component: {fileID: 5896864949079565238}
  - component: {fileID: 578131161280155898}
  - component: {fileID: 1900947399421955647}
  - component: {fileID: 2910694102237855467}
  - component: {fileID: 6588009785077034202}
  - component: {fileID: 2764312118690966655}
  - component: {fileID: 7728839288554511724}
  - component: {fileID: 52626308733956389}
  - component: {fileID: 3172118192777571553}
  - component: {fileID: 4996886478182327313}
  - component: {fileID: 8792042288588277988}
  - component: {fileID: 1500733470876344708}
  - component: {fileID: 2028292967626791357}
  - component: {fileID: 2513663152168031496}
  - component: {fileID: 1616001005337481115}
  - component: {fileID: 7729844532494866945}
  - component: {fileID: 2185156849042567061}
  - component: {fileID: 7049645532705708907}
  - component: {fileID: 7991590769897112120}
  - component: {fileID: 965594366472971441}
  - component: {fileID: 729960228583671371}
  - component: {fileID: 7128071143386416880}
  - component: {fileID: 6040704323107350140}
  - component: {fileID: 1647070127032234144}
  - component: {fileID: 2732953190195047861}
  - component: {fileID: 1388525863606654674}
  - component: {fileID: 4839029702843598644}
  - component: {fileID: 7257505132555263285}
  - component: {fileID: 3107801271977747901}
  - component: {fileID: 6513295446828830655}
  - component: {fileID: 1433163009628197285}
  - component: {fileID: 4234676206141221954}
  - component: {fileID: 6001444884783685501}
  - component: {fileID: 8393305015094439961}
  - component: {fileID: 1456518924154620914}
  - component: {fileID: 3898492553737548080}
  - component: {fileID: 5166082314619663667}
  - component: {fileID: 7936058332449839021}
  - component: {fileID: 6190965634546065746}
  - component: {fileID: 9062377322346718914}
  - component: {fileID: 5363783540924100711}
  - component: {fileID: 2556005800894960694}
  - component: {fileID: 8867951646701588740}
  - component: {fileID: 3417498715359332870}
  - component: {fileID: 7268971045719628302}
  - component: {fileID: 196245840336176678}
  - component: {fileID: 3600367419990268045}
  - component: {fileID: 197736657630963190}
  - component: {fileID: 5533914518489361483}
  - component: {fileID: 3865444520203245336}
  - component: {fileID: 764856916603627900}
  - component: {fileID: 2419955684095779554}
  - component: {fileID: 7656318606293520904}
  - component: {fileID: 1553621668799070860}
  - component: {fileID: 5447299633685983182}
  - component: {fileID: 829809978328492569}
  - component: {fileID: 6043727481365311029}
  - component: {fileID: 2718641561908699220}
  - component: {fileID: 1715838697165196542}
  - component: {fileID: 3845229813606119963}
  - component: {fileID: 1697128748626478825}
  - component: {fileID: 4250665851242821748}
  - component: {fileID: 7353044560693942903}
  - component: {fileID: 3484657648035792460}
  - component: {fileID: 2858200226682978070}
  - component: {fileID: 1709154627942412361}
  m_Layer: 0
  m_Name: StartDialogue
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3321059571650704831
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8040565083048671696
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a334fe2ffb574b3583ff3b18b4792d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 1
  scrollPos: {x: 309.46255, y: -253.44928}
  variablesScrollPos: {x: 0, y: 0}
  variablesExpanded: 1
  blockViewHeight: 400
  zoom: 0.6100004
  scrollViewRect:
    serializedVersion: 2
    x: -343
    y: -340
    width: 1114
    height: 859
  selectedBlocks: []
  selectedCommands: []
  variables:
  - {fileID: 7991590769897112120}
  - {fileID: 965594366472971441}
  - {fileID: 729960228583671371}
  - {fileID: 7128071143386416880}
  - {fileID: 6040704323107350140}
  description: 
  stepPause: 0
  colorCommands: 1
  hideComponents: 1
  saveSelection: 1
  localizationId: 
  showLineNumbers: 0
  hideCommands: []
  luaEnvironment: {fileID: 0}
  luaBindingName: flowchart
--- !u!114 &4087058147526112350
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 120.872345
    y: 49.53617
    width: 130.32782
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 0
  blockName: StartDialogue
  description: 
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 1500733470876344708}
  - {fileID: 2028292967626791357}
  - {fileID: 7090517491754809714}
  - {fileID: 599364693722615891}
  - {fileID: 4192772015813394289}
  - {fileID: 2371872450066642155}
  - {fileID: 8184919673541066407}
  - {fileID: 3184283698437494502}
  - {fileID: 5896864949079565238}
  - {fileID: 578131161280155898}
  - {fileID: 1900947399421955647}
  - {fileID: 2910694102237855467}
  - {fileID: 6588009785077034202}
  - {fileID: 2764312118690966655}
  - {fileID: 7728839288554511724}
  - {fileID: 8792042288588277988}
  - {fileID: 52626308733956389}
  - {fileID: 3172118192777571553}
  - {fileID: 4996886478182327313}
  - {fileID: 2513663152168031496}
  suppressAllAutoSelections: 0
--- !u!114 &7090517491754809714
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 1
  indentLevel: 0
  storyText: "\u4F60\u5C31\u662F\u9648\u660E\u544A\u4E13\u5BB6\u5417\uFF1F"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &599364693722615891
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 2
  indentLevel: 0
  storyText: "\u662F\u7684\uFF0C\u4F60\u5C31\u662F\u67F3\u9986\u957F\u7684{color=red}\u5916\u5B59\u5973{/color}\uFF0C\u5BF9\u5417\uFF1F"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 21300000, guid: f9677df822715184f9b48f81ae0b011a, type: 3}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &4192772015813394289
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 3
  indentLevel: 0
  storyText: "\u5BF9\uFF0C\u540C\u65F6\u6211\u4E5F\u662F\u516C\u5B89\u5C40\u7684\u4E00\u540D\u8B66\u5458\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &2371872450066642155
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 4
  indentLevel: 0
  storyText: "\u4F60\u8FD9\u4E48\u7740\u6025\u627E\u6211\u60F3\u5FC5\u6709\u4EC0\u4E48\u8981\u7D27\u7684\u4E8B\u5427\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 21300000, guid: f9677df822715184f9b48f81ae0b011a, type: 3}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &8184919673541066407
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 5
  indentLevel: 0
  storyText: "\u5BF9\uFF0C\u9648\u4E13\u5BB6\uFF0C\u6211\u957F\u8BDD\u77ED\u8BF4\uFF0C\u67F3\u9986\u957F\u5931\u8E2A\u4E86\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &3184283698437494502
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 6
  indentLevel: 0
  storyText: "\u4E0D\u53EF\u80FD\u554A\uFF0C\u4ED6\u524D\u51E0\u5929\u8FD8\u7EA6\u6211\u53C2\u52A0\u540E\u5929\u7684\u65B0\u5C55\u5462\uFF0C\u8BF4\u4ED6\u8FD9\u6B21\u65B0\u5C55\u53EF\u662F\u82B1\u4E86\u5927\u529B\u6C14\uFF0C\u8BA9\u6211\u597D\u597D\u671F\u5F85\u4E00\u4E0B\u5462\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &5896864949079565238
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 7
  indentLevel: 0
  storyText: "\u5BF9\uFF0C\u6211\u4E5F\u8BA4\u4E3A\u90A3\u8001\u5934\u5B50\u4E0D\u53EF\u80FD\u5931\u8E2A\uFF0C\u6BD5\u7ADF\u6587\u7269\u624D\u662F\u4ED6\u6700\u5B9D\u8D35\u7684\u4E1C\u897F\uFF0C\u66F4\u4F55\u51B5\u540E\u5929\u8FD8\u6709\u65B0\u5C55\uFF0C\u4ED6\u4E0D\u53EF\u80FD\u83AB\u540D\u5176\u5999\u7684\u5931\u8E2A\uFF0C\u8FD9\u5176\u4E2D\u4E00\u5B9A\u6709\u9690\u60C5\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &578131161280155898
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 8
  indentLevel: 0
  storyText: "\u90A3\u4F60\u4E0D\u6293\u7D27\u65F6\u95F4\u8C03\u67E5\uFF0C\u4E3A\u4EC0\u4E48\u8FD9\u4E48\u6025\u5FD9\u7684\u6765\u627E\u6211\uFF1F"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &1900947399421955647
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 9
  indentLevel: 0
  storyText: "\u73B0\u573A\u5F88\u5E72\u51C0\uFF0C\u8B66\u65B9\u4ED6\u4EEC\u56E0\u6B64\u63A8\u5B9A\u662F\u81EA\u5DF1\u8D70\u5931\u7684\uFF0C\u540C\u65F6\u6211\u56E0\u4E3A\u4EB2\u5C5E\u5173\u7CFB\uFF0C\u4E0D\u5F97\u53C2\u4E0E\u6B64\u6B21\u8FD9\u6B21\u8C03\u67E5\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &2910694102237855467
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 10
  indentLevel: 0
  storyText: "\u4F46\u4F60\u53C8\u8BA4\u4E3A\u6709\u9690\u60C5\uFF0C\u6240\u4EE5\u60F3{color=red}\u79C1\u4E0B\u8C03\u67E5{/color}\uFF0C\u6211\u6BD4\u8F83\u597D\u5947\u7684\u662F\u2026\u2026\u4E3A\u4EC0\u4E48\u627E\u6211\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &6588009785077034202
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 11
  indentLevel: 0
  storyText: "\u56E0\u4E3A\u5916\u516C\u53BB\u4E16\u5F53\u5929\u7684\u65E5\u8BB0\u4E0A\u6709\u4E00\u53E5\u8BDD\uFF1A\u77E5\u6B7B\u4E0D\u53EF\u8BA9\uFF0C\u613F\u52FF\u7231\u516E\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &2764312118690966655
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 12
  indentLevel: 0
  storyText: "\u8FD9\u662F\u5C48\u539F\u7684\u300A\u4E5D\u7AE0\xB7\u6000\u6C99\u300B\uFF0C\u4E0B\u4E00\u53E5\u662F{color=red}\u660E\u544A\u541B\u5B50{/color}\uFF0C\u543E\u5C06\u4EE5\u4E3A\u7C7B\u516E\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &7728839288554511724
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 13
  indentLevel: 0
  storyText: "\u5BF9\uFF0C\u8FD9\u5C31\u662F\u6211\u6765\u627E\u4F60\u9648\u660E\u544A\u7684\u539F\u56E0\uFF0C\u4ED6\u4E34\u6B7B\u524D\u7559\u4E0B\u7684\u8FD9\u53E5\u8BDD\u53EF\u80FD\u5DF2\u7ECF\u9884\u611F\u5230\u81EA\u5DF1\u7684\u5371\u9669\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &52626308733956389
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 14
  indentLevel: 0
  storyText: "\u90A3\u4F60\u4E3A\u4EC0\u4E48\u4E0D\u6000\u7591\u6211\u662F\u51F6\u624B\uFF1F"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &3172118192777571553
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 15
  indentLevel: 0
  storyText: "\u60C5\u51B5\u7D27\u6025\uFF0C\u6211\u5C31\u76F4\u8BF4\u4E86\uFF0C\u6211\u5DF2\u7ECF\u8C03\u67E5\u8FC7\u4F60\u4E86\uFF0C\u4F60\u6CA1\u6709\u4F5C\u6848\u65F6\u95F4\uFF0C\u540C\u65F6\u4E5F\u559C\u7231\u6587\u7269\uFF0C\u548C\u67F3\u9986\u957F\u662F\u4E00\u7C7B\u4EBA\u3002"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &4996886478182327313
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 16
  indentLevel: 0
  storyText: "\u8FD8\u771F\u662F\u4EE4\u4EBA\u4FE1\u670D\u7684\u7406\u7531\uFF0C\u6211\u4E5F\u786E\u5B9E\u4E0D\u80FD\u8BA9\u67F3\u9986\u957F\u5C31\u8FD9\u4E48\u4E0D\u660E\u4E0D\u767D\u7684\u6B7B\u4E86\uFF0C\u90A3\u4E0B\u4E00\u6B65\u6211\u4EEC\u8BE5\u600E\u4E48\u529E\uFF1F"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &8792042288588277988
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 17
  indentLevel: 0
  storyText: "\u540C\u65F6\u6211\u8C03\u67E5\u4E86\u4ED6\u7684\u4EBA\u9645\u5173\u7CFB\uFF0C\u53D1\u73B0\u4F60\u8FD1\u4E24\u5E74\u548C\u4ED6\u4EA4\u5F80\u6BD4\u8F83\u5BC6\u5207\uFF0C\u540C\u65F6\u4E5F\u662F\u6587\u7269\u4E13\u5BB6\uFF0C\u6240\u4EE5\u2026\u2026"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &1500733470876344708
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 18
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &2028292967626791357
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 19
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: GameManager, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
    PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: GameManager
  targetMethod: DisablePlayerMovement
  targetMethodText: 'DisablePlayerMovement (): Void'
  methodParameters: []
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &2513663152168031496
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 20
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: GameManager, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
    PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: GameManager
  targetMethod: EnablePlayerMovement
  targetMethodText: 'EnablePlayerMovement (): Void'
  methodParameters: []
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &1616001005337481115
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: -206.57718
    y: 426.52728
    width: 124.590126
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 21
  blockName: ChangeView
  description: "\u53BB\u5F80\u4FDD\u5B89\u5BA4"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 7729844532494866945}
  - {fileID: 5166082314619663667}
  - {fileID: 7936058332449839021}
  suppressAllAutoSelections: 0
--- !u!114 &7729844532494866945
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 437f9a4e3dbc647f9bdce95308418bff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 23
  indentLevel: 0
  duration: 2
  fadeOut: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  fadeColor: {r: 0, g: 0, b: 0, a: 1}
  fadeTexture: {fileID: 0}
  targetCamera: {fileID: 0}
  fadeTweenType: 4
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &2185156849042567061
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 565.0409
    y: 90.40192
    width: 127.86881
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 24
  blockName: LockedBlock
  description: 
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 7049645532705708907}
  suppressAllAutoSelections: 0
--- !u!114 &7049645532705708907
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 25
  indentLevel: 0
  storyText: "\u4F3C\u4E4E\u9700\u8981\u94A5\u5319"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 21300000, guid: f9677df822715184f9b48f81ae0b011a, type: 3}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &7991590769897112120
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4580f28dd8581476b810b38eea2f1316, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  scope: 0
  key: password
  value: 1234
--- !u!114 &965594366472971441
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4580f28dd8581476b810b38eea2f1316, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  scope: 0
  key: input
  value: 
--- !u!114 &729960228583671371
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5d02d9822eec54c98afe95bb497211b3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  scope: 0
  key: PuzzleSolved
  value: 0
--- !u!114 &7128071143386416880
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: afb91b566ceda411bad1e9d3c3243ecc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  scope: 0
  key: AttemptCount
  value: 0
--- !u!114 &6040704323107350140
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: afb91b566ceda411bad1e9d3c3243ecc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  scope: 0
  key: PasswordLength
  value: 4
--- !u!114 &1647070127032234144
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 622.514
    y: 184.5003
    width: 171.31142
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 26
  blockName: PasswordPuzzleStart
  description: 
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 2732953190195047861}
  - {fileID: 1388525863606654674}
  suppressAllAutoSelections: 0
--- !u!114 &2732953190195047861
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 27
  indentLevel: 0
  storyText: "\u9700\u8981\u5BC6\u7801"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &1388525863606654674
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 28
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: PasswordPuzzleManager, Assembly-CSharp, Version=0.0.0.0,
    Culture=neutral, PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: PasswordPuzzleManager
  targetMethod: ShowPasswordInput
  targetMethodText: 'ShowPasswordInput (): Void'
  methodParameters: []
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &4839029702843598644
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 798.87646
    y: 184.4425
    width: 144.26224
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 29
  blockName: CheckPassword
  description: 
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 7257505132555263285}
  suppressAllAutoSelections: 0
--- !u!114 &7257505132555263285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 30
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: PasswordPuzzleManager, Assembly-CSharp, Version=0.0.0.0,
    Culture=neutral, PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: PasswordPuzzleManager
  targetMethod: CheckPassword
  targetMethodText: 'CheckPassword (): Void'
  methodParameters: []
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &3107801271977747901
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 961.8491
    y: 127.89307
    width: 149.18027
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 34
  blockName: PasswordCorrect
  description: 
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 6513295446828830655}
  suppressAllAutoSelections: 0
--- !u!114 &6513295446828830655
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 32
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: GameManager, Assembly-CSharp, Version=0.0.0.0, Culture=neutral,
    PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: GameManager
  targetMethod: EnablePlayerMovement
  targetMethodText: 'EnablePlayerMovement (): Void'
  methodParameters: []
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &1433163009628197285
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 964.1755
    y: 235.90753
    width: 142.6229
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 35
  blockName: PasswordWrong
  description: 
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 4234676206141221954}
  suppressAllAutoSelections: 0
--- !u!114 &4234676206141221954
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec422cd568a9c4a31ad7c36d0572b9da, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 33
  indentLevel: 0
  storyText: "\u5BC6\u7801\u9519\u8BEF"
  description: 
  character: {fileID: 0}
  portrait: {fileID: 0}
  voiceOverClip: {fileID: 0}
  showAlways: 1
  showCount: 1
  extendPrevious: 0
  fadeWhenDone: 1
  waitForClick: 1
  stopVoiceover: 1
  waitForVO: 0
  setSayDialog: {fileID: 0}
--- !u!114 &6001444884783685501
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 335.809
    y: 41.94123
    width: 172.95076
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 57
  blockName: StartDialogue (Copy)
  description: 
  eventHandler: {fileID: 3898492553737548080}
  commandList:
  - {fileID: 8393305015094439961}
  - {fileID: 1456518924154620914}
  suppressAllAutoSelections: 0
--- !u!114 &8393305015094439961
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 37
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &1456518924154620914
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 56
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: 
  targetComponentFullname: 
  targetComponentText: 
  targetMethod: 
  targetMethodText: 
  methodParameters: []
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: 
  showInherited: 0
  callMode: 0
--- !u!114 &3898492553737548080
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d2f6487d21a03404cb21b245f0242e79, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  parentBlock: {fileID: 6001444884783685501}
  suppressBlockAutoSelect: 0
  waitForFrames: 1
--- !u!114 &5166082314619663667
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 58
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: PlayerController, Assembly-CSharp, Version=0.0.0.0,
    Culture=neutral, PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: PlayerController
  targetMethod: SetPlayerTransform
  targetMethodText: 'SetPlayerTransform (Int32): Void'
  methodParameters:
  - objValue:
      typeAssemblyname: System.Int32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
      typeFullname: System.Int32
      intValue: 0
      boolValue: 0
      floatValue: 0
      stringValue: 
      colorValue: {r: 0, g: 0, b: 0, a: 0}
      gameObjectValue: {fileID: 0}
      materialValue: {fileID: 0}
      objectValue: {fileID: 0}
      spriteValue: {fileID: 0}
      textureValue: {fileID: 0}
      vector2Value: {x: 0, y: 0}
      vector3Value: {x: 0, y: 0, z: 0}
    variableKey: 
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &7936058332449839021
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 59
  indentLevel: 0
  duration: 2
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &6190965634546065746
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 278.51584
    y: 488.49496
    width: 124.590126
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 60
  blockName: ViewChange
  description: "\u9986\u957F\u529E\u516C\u5BA41"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 9062377322346718914}
  suppressAllAutoSelections: 0
--- !u!114 &9062377322346718914
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 61
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &5363783540924100711
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: -208.23453
    y: 491.40973
    width: 127.58616
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 66
  blockName: ChangeView2
  description: "\u53BB\u5F80\u9986\u957F\u529E\u516C\u5BA4"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 2556005800894960694}
  - {fileID: 8867951646701588740}
  - {fileID: 3417498715359332870}
  suppressAllAutoSelections: 0
--- !u!114 &2556005800894960694
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 437f9a4e3dbc647f9bdce95308418bff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 63
  indentLevel: 0
  duration: 2
  fadeOut: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  fadeColor: {r: 0, g: 0, b: 0, a: 1}
  fadeTexture: {fileID: 0}
  targetCamera: {fileID: 0}
  fadeTweenType: 4
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &8867951646701588740
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 64
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: PlayerController, Assembly-CSharp, Version=0.0.0.0,
    Culture=neutral, PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: PlayerController
  targetMethod: SetPlayerTransform
  targetMethodText: 'SetPlayerTransform (Int32): Void'
  methodParameters:
  - objValue:
      typeAssemblyname: System.Int32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
      typeFullname: System.Int32
      intValue: 2
      boolValue: 0
      floatValue: 0
      stringValue: 
      colorValue: {r: 0, g: 0, b: 0, a: 0}
      gameObjectValue: {fileID: 0}
      materialValue: {fileID: 0}
      objectValue: {fileID: 0}
      spriteValue: {fileID: 0}
      textureValue: {fileID: 0}
      vector2Value: {x: 0, y: 0}
      vector3Value: {x: 0, y: 0, z: 0}
    variableKey: 
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &3417498715359332870
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 65
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &7268971045719628302
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 269.26587
    y: 556.6306
    width: 127.58616
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 69
  blockName: ViewChange2
  description: "\u9986\u957F\u529E\u516C\u5BA42"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 196245840336176678}
  suppressAllAutoSelections: 0
--- !u!114 &196245840336176678
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 68
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &3600367419990268045
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: -211.87088
    y: 574.0909
    width: 128.44823
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 78
  blockName: ChangeView3
  description: "\u53BB\u5F80\u535A\u7269\u9986\u4E00\u697C"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 197736657630963190}
  - {fileID: 5533914518489361483}
  - {fileID: 3865444520203245336}
  suppressAllAutoSelections: 0
--- !u!114 &197736657630963190
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 437f9a4e3dbc647f9bdce95308418bff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 71
  indentLevel: 0
  duration: 2
  fadeOut: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  fadeColor: {r: 0, g: 0, b: 0, a: 1}
  fadeTexture: {fileID: 0}
  targetCamera: {fileID: 0}
  fadeTweenType: 4
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &5533914518489361483
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 72
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: PlayerController, Assembly-CSharp, Version=0.0.0.0,
    Culture=neutral, PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: PlayerController
  targetMethod: SetPlayerTransform
  targetMethodText: 'SetPlayerTransform (Int32): Void'
  methodParameters:
  - objValue:
      typeAssemblyname: System.Int32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
      typeFullname: System.Int32
      intValue: 1
      boolValue: 0
      floatValue: 0
      stringValue: 
      colorValue: {r: 0, g: 0, b: 0, a: 0}
      gameObjectValue: {fileID: 0}
      materialValue: {fileID: 0}
      objectValue: {fileID: 0}
      spriteValue: {fileID: 0}
      textureValue: {fileID: 0}
      vector2Value: {x: 0, y: 0}
      vector3Value: {x: 0, y: 0, z: 0}
    variableKey: 
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &3865444520203245336
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 73
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &764856916603627900
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: -210.96162
    y: 654.091
    width: 128.44823
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 79
  blockName: ChangeView4
  description: "\u53BB\u5F80\u535A\u7269\u9986\u4E8C\u697C"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 2419955684095779554}
  - {fileID: 7656318606293520904}
  - {fileID: 1553621668799070860}
  suppressAllAutoSelections: 0
--- !u!114 &2419955684095779554
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 437f9a4e3dbc647f9bdce95308418bff, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 75
  indentLevel: 0
  duration: 2
  fadeOut: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  fadeColor: {r: 0, g: 0, b: 0, a: 1}
  fadeTexture: {fileID: 0}
  targetCamera: {fileID: 0}
  fadeTweenType: 4
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &7656318606293520904
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 688e35811870d403f9e2b1ab2a699d98, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 76
  indentLevel: 0
  description: 
  targetObject: {fileID: 0}
  targetComponentAssemblyName: PlayerController, Assembly-CSharp, Version=0.0.0.0,
    Culture=neutral, PublicKeyToken=null
  targetComponentFullname: UnityEngine.Component[]
  targetComponentText: PlayerController
  targetMethod: SetPlayerTransform
  targetMethodText: 'SetPlayerTransform (Int32): Void'
  methodParameters:
  - objValue:
      typeAssemblyname: System.Int32, mscorlib, Version=*******, Culture=neutral,
        PublicKeyToken=b77a5c561934e089
      typeFullname: System.Int32
      intValue: 3
      boolValue: 0
      floatValue: 0
      stringValue: 
      colorValue: {r: 0, g: 0, b: 0, a: 0}
      gameObjectValue: {fileID: 0}
      materialValue: {fileID: 0}
      objectValue: {fileID: 0}
      spriteValue: {fileID: 0}
      textureValue: {fileID: 0}
      vector2Value: {x: 0, y: 0}
      vector3Value: {x: 0, y: 0, z: 0}
    variableKey: 
  saveReturnValue: 0
  returnValueVariableKey: 
  returnValueType: System.Void
  showInherited: 0
  callMode: 0
--- !u!114 &1553621668799070860
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 77
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &5447299633685983182
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 439.2659
    y: 490.20148
    width: 128.44823
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 92
  blockName: ViewChange3
  description: "\u535A\u7269\u9986\u4E00\u697C1"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 829809978328492569}
  suppressAllAutoSelections: 0
--- !u!114 &829809978328492569
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 81
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &6043727481365311029
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 437.12292
    y: 561.63025
    width: 128.44823
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 93
  blockName: ViewChange4
  description: "\u535A\u7269\u9986\u4E00\u697C2"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 2718641561908699220}
  suppressAllAutoSelections: 0
--- !u!114 &2718641561908699220
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 83
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &1715838697165196542
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 437.83704
    y: 642.34515
    width: 127.58616
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 94
  blockName: ViewChange5
  description: "\u535A\u7269\u9986\u4E00\u697C3"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 3845229813606119963}
  suppressAllAutoSelections: 0
--- !u!114 &3845229813606119963
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 85
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &1697128748626478825
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 620.86487
    y: 491.1478
    width: 127.58616
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 95
  blockName: ViewChange6
  description: "\u535A\u7269\u9986\u4E8C\u697C1"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 4250665851242821748}
  suppressAllAutoSelections: 0
--- !u!114 &4250665851242821748
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 87
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &7353044560693942903
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 618.7219
    y: 562.57654
    width: 127.58616
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 96
  blockName: ViewChange7
  description: "\u535A\u7269\u9986\u4E8C\u697C2"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 3484657648035792460}
  suppressAllAutoSelections: 0
--- !u!114 &3484657648035792460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 89
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
--- !u!114 &2858200226682978070
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3d3d73aef2cfc4f51abf34ac00241f60, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  nodeRect:
    serializedVersion: 2
    x: 619.43604
    y: 643.2915
    width: 127.58616
    height: 40
  tint: {r: 1, g: 1, b: 1, a: 1}
  useCustomTint: 0
  itemId: 97
  blockName: ViewChange8
  description: "\u535A\u7269\u9986\u4E8C\u697C3"
  eventHandler: {fileID: 0}
  commandList:
  - {fileID: 1709154627942412361}
  suppressAllAutoSelections: 0
--- !u!114 &1709154627942412361
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1890220114624908046}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45c952ea1ad444e479b570fa242679c5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  itemId: 91
  indentLevel: 0
  duration: 1
  targetView: {fileID: 0}
  waitUntilFinished: 1
  targetCamera: {fileID: 0}
  orthoSizeTweenType: 4
  posTweenType: 4
  rotTweenType: 4
