using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Fungus;

public class Door : InteractableItem
{
    [Header("门的特殊设置")]
    public string lockedBlockName; // 门锁住时触发的对话块
    public bool isLocked = true; // 门是否锁住
    
    // 重写Interact方法来检查钥匙
    protected override void Interact()
    {
        if (flowchart == null) return;
        
        // 触发对话前禁用玩家移动
        GameManager.Instance.DisablePlayerMovement();
        
        if (isLocked)
        {
            // 查找玩家控制器
            PlayerController player = GameManager.Instance.player;
            
            // 检查玩家是否有钥匙
            if (player != null && player.HasKey)
            {
                // 有钥匙，解锁门并执行正常的开门对话
                isLocked = false;
                flowchart.ExecuteBlock(blockName);
            }
            else
            {
                // 没有钥匙，执行锁住状态的对话
                flowchart.ExecuteBlock(lockedBlockName);
            }
        }
        else
        {
            // 门已解锁，执行正常的开门对话
            flowchart.ExecuteBlock(blockName);
        }
    }
    
    // 可以添加一个公共方法供其他脚本调用来解锁门
    public void Unlock()
    {
        isLocked = false;
    }
    
    // 可以添加一个方法来锁门
    public void Lock()
    {
        isLocked = true;
    }
}