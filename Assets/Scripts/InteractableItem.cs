using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Fungus;

public class InteractableItem : MonoBehaviour
{
    public string itemName;
    public Flowchart flowchart;
    public string blockName;
    public KeyCode interactionKey = KeyCode.E; // 默认交互键
    public GameObject interactionPrompt; // 交互提示UI
    public Vector2 promptOffset = new Vector2(0, 1.5f); // UI相对于物体的偏移
    public bool followObject = true; // 是否跟随物体移动
    public Canvas uiCanvas; // 如果使用世界空间UI，可以不设置

    private bool playerInRange = false;
    private RectTransform promptRectTransform;
    private Camera mainCamera;

    void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            playerInRange = true;
            ShowInteractionHint(true);
        }
    }

    void OnTriggerExit2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            playerInRange = false;
            ShowInteractionHint(false);
        }
    }

    void Start()
    {
        // 确保开始时提示不可见
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(false);
            promptRectTransform = interactionPrompt.GetComponent<RectTransform>();
        }
        
        mainCamera = Camera.main;
        
        // 如果没有指定Canvas，尝试查找
        if (uiCanvas == null)
        {
            uiCanvas = FindObjectOfType<Canvas>();
        }
    }

    void Update()
    {
        if (playerInRange)
        {
            if (followObject && interactionPrompt != null && interactionPrompt.activeSelf)
            {
                UpdatePromptPosition();
            }
            
            if (Input.GetKeyDown(interactionKey))
            {
                Interact();
            }
        }
    }
    
    void UpdatePromptPosition()
    {
        if (promptRectTransform == null) return;
        
        // 计算提示UI应该显示的世界坐标
        Vector3 worldPos = transform.position + new Vector3(promptOffset.x, promptOffset.y, 0);
        
        // 如果是屏幕空间UI，需要转换为屏幕坐标
        if (uiCanvas != null && uiCanvas.renderMode != RenderMode.WorldSpace)
        {
            Vector2 screenPos = RectTransformUtility.WorldToScreenPoint(mainCamera, worldPos);
            Vector2 localPos;
            
            // 转换为Canvas中的本地坐标
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                uiCanvas.GetComponent<RectTransform>(), 
                screenPos, 
                uiCanvas.worldCamera, 
                out localPos))
            {
                promptRectTransform.localPosition = localPos;
            }
        }
        else // 世界空间UI直接设置位置
        {
            promptRectTransform.position = worldPos;
        }
    }
    
    protected virtual void Interact()
    {
        if (flowchart != null)
        {
            // 触发对话前禁用玩家移动
            GameManager.Instance.DisablePlayerMovement();

            // 执行Fungus对话块
            flowchart.ExecuteBlock(blockName);
        }
    }
    
    void ShowInteractionHint(bool show)
    {
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(show);
            
            // 立即更新位置
            if (show && followObject)
            {
                UpdatePromptPosition();
            }
        }
    }
}
