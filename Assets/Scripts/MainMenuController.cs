using UnityEngine;
using UnityEngine.UI;
using Fungus;

/// <summary>
/// 主菜单控制器
/// 处理开始界面的按键功能，与VideoController配合播放开场视频
/// </summary>
public class MainMenuController : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private GameObject mainMenuUI;
    [SerializeField] private Button startButton;
    [SerializeField] private Button exitButton;
    
    [Header("视频控制")]
    [SerializeField] private VideoController videoController;
    [SerializeField] private int openingVideoIndex = 0; // 开场视频索引
    
    [Header("Fungus集成")]
    [SerializeField] private Flowchart flowchart;
    [SerializeField] private string afterVideoBlockName = "AfterOpeningVideo"; // 视频播放完成后执行的Block

    [Header("设置")]
    [SerializeField] private bool enableDebugLog = true;
    [SerializeField] private float exitConfirmDelay = 0.5f; // 退出确认延迟
    
    void Start()
    {
        SetupButtons();
        ValidateComponents();
    }
    
    /// <summary>
    /// 设置按钮事件
    /// </summary>
    void SetupButtons()
    {
        if (startButton != null)
        {
            startButton.onClick.AddListener(OnStartButtonClicked);
            if (enableDebugLog)
            {
                Debug.Log("[MainMenuController] 开始按钮事件已设置");
            }
        }
        else
        {
            Debug.LogWarning("[MainMenuController] 开始按钮未设置！");
        }
        
        if (exitButton != null)
        {
            exitButton.onClick.AddListener(OnExitButtonClicked);
            if (enableDebugLog)
            {
                Debug.Log("[MainMenuController] 退出按钮事件已设置");
            }
        }
        else
        {
            Debug.LogWarning("[MainMenuController] 退出按钮未设置！");
        }
    }
    
    /// <summary>
    /// 验证组件设置
    /// </summary>
    void ValidateComponents()
    {
        if (videoController == null)
        {
            videoController = FindObjectOfType<VideoController>();
            if (videoController == null)
            {
                Debug.LogWarning("[MainMenuController] 未找到VideoController组件！");
            }
        }

        if (flowchart == null)
        {
            flowchart = FindObjectOfType<Flowchart>();
            if (flowchart == null)
            {
                Debug.LogWarning("[MainMenuController] 未找到Flowchart组件！");
            }
        }

        if (mainMenuUI == null)
        {
            Debug.LogWarning("[MainMenuController] 主菜单UI未设置！");
        }
    }
    
    /// <summary>
    /// 开始按钮点击事件
    /// </summary>
    public void OnStartButtonClicked()
    {
        if (enableDebugLog)
        {
            Debug.Log("[MainMenuController] 开始按钮被点击");
        }
        
        // 隐藏主菜单UI
        HideMainMenu();
        
        // 播放开场视频
        PlayOpeningVideo();
    }
    
    /// <summary>
    /// 退出按钮点击事件
    /// </summary>
    public void OnExitButtonClicked()
    {
        if (enableDebugLog)
        {
            Debug.Log("[MainMenuController] 退出按钮被点击");
        }
        
        // 延迟退出，给用户一些反馈时间
        Invoke(nameof(ExitGame), exitConfirmDelay);
    }
    
    /// <summary>
    /// 隐藏主菜单UI
    /// </summary>
    void HideMainMenu()
    {
        if (mainMenuUI != null)
        {
            mainMenuUI.SetActive(false);
            
            if (enableDebugLog)
            {
                Debug.Log("[MainMenuController] 主菜单UI已隐藏");
            }
        }
    }
    
    /// <summary>
    /// 显示主菜单UI
    /// </summary>
    public void ShowMainMenu()
    {
        if (mainMenuUI != null)
        {
            mainMenuUI.SetActive(true);
            
            if (enableDebugLog)
            {
                Debug.Log("[MainMenuController] 主菜单UI已显示");
            }
        }
    }
    
    /// <summary>
    /// 播放开场视频
    /// </summary>
    void PlayOpeningVideo()
    {
        if (videoController == null)
        {
            Debug.LogError("[MainMenuController] VideoController未设置，无法播放视频！");
            return;
        }

        // 显示视频屏幕
        videoController.ShowVideoScreen();

        // 播放指定索引的视频
        if (videoController.GetVideoCount() > openingVideoIndex)
        {
            videoController.PlayVideo(openingVideoIndex);

            if (enableDebugLog)
            {
                Debug.Log($"[MainMenuController] 开始播放开场视频，索引: {openingVideoIndex}");
            }

            // 监听视频播放完成并触发Block
            StartCoroutine(WaitForVideoAndTriggerBlock());
        }
        else
        {
            Debug.LogError($"[MainMenuController] 视频索引 {openingVideoIndex} 超出范围！");
        }
    }
    
    /// <summary>
    /// 等待视频播放完成并触发Fungus Block
    /// </summary>
    System.Collections.IEnumerator WaitForVideoAndTriggerBlock()
    {
        // 等待视频开始播放
        yield return new WaitForSeconds(0.1f);

        // 等待视频播放完成
        while (videoController.videoPlayer.isPlaying)
        {
            yield return null;
        }

        if (enableDebugLog)
        {
            Debug.Log("[MainMenuController] 开场视频播放完成，触发Fungus Block");
        }

        // 隐藏视频屏幕
        videoController.HideVideoScreen();

        // 触发Fungus Block
        TriggerAfterVideoBlock();
    }

    /// <summary>
    /// 触发视频播放完成后的Block
    /// </summary>
    void TriggerAfterVideoBlock()
    {
        if (flowchart == null)
        {
            Debug.LogError("[MainMenuController] Flowchart未设置，无法触发Block！");
            return;
        }

        if (string.IsNullOrEmpty(afterVideoBlockName))
        {
            Debug.LogError("[MainMenuController] After Video Block Name未设置！");
            return;
        }

        if (flowchart.HasBlock(afterVideoBlockName))
        {
            if (enableDebugLog)
            {
                Debug.Log($"[MainMenuController] 触发Block: {afterVideoBlockName}");
            }

            flowchart.ExecuteBlock(afterVideoBlockName);
        }
        else
        {
            Debug.LogError($"[MainMenuController] 未找到Block: {afterVideoBlockName}");
        }
    }
    
    /// <summary>
    /// 退出游戏
    /// </summary>
    void ExitGame()
    {
        if (enableDebugLog)
        {
            Debug.Log("[MainMenuController] 退出游戏");
        }
        
#if UNITY_EDITOR
        // 在编辑器中停止播放
        UnityEditor.EditorApplication.isPlaying = false;
#else
        // 在构建版本中退出应用程序
        Application.Quit();
#endif
    }
    
    /// <summary>
    /// 跳过视频（可选功能）
    /// </summary>
    public void SkipVideo()
    {
        if (videoController != null && videoController.videoPlayer.isPlaying)
        {
            videoController.StopVideo();
            videoController.HideVideoScreen();

            // 跳过视频时也触发Block
            TriggerAfterVideoBlock();

            if (enableDebugLog)
            {
                Debug.Log("[MainMenuController] 视频已跳过");
            }
        }
    }
    
    /// <summary>
    /// 返回主菜单（从其他界面调用）
    /// </summary>
    public void ReturnToMainMenu()
    {
        // 停止视频播放
        if (videoController != null)
        {
            videoController.StopVideo();
            videoController.HideVideoScreen();
        }
        
        // 显示主菜单
        ShowMainMenu();
        
        if (enableDebugLog)
        {
            Debug.Log("[MainMenuController] 返回主菜单");
        }
    }
    
    /// <summary>
    /// 设置视频索引
    /// </summary>
    public void SetOpeningVideoIndex(int index)
    {
        openingVideoIndex = index;
        
        if (enableDebugLog)
        {
            Debug.Log($"[MainMenuController] 开场视频索引设置为: {index}");
        }
    }
    
    /// <summary>
    /// 设置视频播放完成后的Block名称
    /// </summary>
    public void SetAfterVideoBlockName(string blockName)
    {
        afterVideoBlockName = blockName;

        if (enableDebugLog)
        {
            Debug.Log($"[MainMenuController] 视频完成后Block名称设置为: {blockName}");
        }
    }
    
    void Update()
    {
        // 支持ESC键返回主菜单或退出
        if (Input.GetKeyDown(KeyCode.Escape))
        {
            if (mainMenuUI != null && mainMenuUI.activeInHierarchy)
            {
                // 如果在主菜单，按ESC退出游戏
                OnExitButtonClicked();
            }
            else if (videoController != null && videoController.videoPlayer.isPlaying)
            {
                // 如果正在播放视频，按ESC跳过视频
                SkipVideo();
            }
            else
            {
                // 如果不在主菜单，按ESC返回主菜单
                ReturnToMainMenu();
            }
        }
        
        // 支持空格键跳过视频
        if (Input.GetKeyDown(KeyCode.Space))
        {
            if (videoController != null && videoController.videoPlayer.isPlaying)
            {
                SkipVideo();
            }
        }
    }
}
