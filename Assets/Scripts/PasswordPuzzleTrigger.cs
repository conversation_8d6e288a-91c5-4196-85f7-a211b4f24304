using UnityEngine;
using Fungus;

/// <summary>
/// 密码谜题管理器
/// 与InteractableItem配合使用，通过Fungus Block触发密码输入界面
/// </summary>
public class PasswordPuzzleManager : MonoBehaviour
{
    [Header("谜题设置")]
    [SerializeField] private string puzzleTitle = "密码锁";
    [SerializeField] private string correctPassword = "1234";
    [SerializeField] private int passwordLength = 4;
    [SerializeField] private int maxAttempts = 3;

    [Header("UI组件")]
    [SerializeField] private PasswordPuzzleUI passwordUI;

    [Header("Fungus集成")]
    [SerializeField] private Flowchart flowchart;

    [Header("Block名称设置")]
    [SerializeField] private string checkPasswordBlockName = "CheckPassword";
    [SerializeField] private string passwordCorrectBlockName = "PasswordCorrect";
    [SerializeField] private string passwordWrongBlockName = "PasswordWrong";
    [SerializeField] private string puzzleCompleteBlockName = "PuzzleComplete";

    private bool puzzleSolved = false;
    private int currentAttempts = 0;

    void Start()
    {
        SetupPuzzle();
    }

    void SetupPuzzle()
    {
        // 设置Fungus变量
        if (flowchart != null)
        {
            // 设置正确密码
            var correctPasswordVar = flowchart.GetVariable<StringVariable>("CorrectPassword");
            if (correctPasswordVar != null)
            {
                correctPasswordVar.Value = correctPassword;
            }
            else
            {
                Debug.LogWarning($"[PasswordPuzzle] 未找到CorrectPassword变量，请在Flowchart中创建");
            }

            // 初始化用户输入变量
            var userInputVar = flowchart.GetVariable<StringVariable>("UserInput");
            if (userInputVar == null)
            {
                Debug.LogWarning($"[PasswordPuzzle] 未找到UserInput变量，请在Flowchart中创建");
            }

            // 初始化谜题状态变量
            var puzzleStateVar = flowchart.GetVariable<BooleanVariable>("PuzzleSolved");
            if (puzzleStateVar != null)
            {
                puzzleStateVar.Value = false;
            }

            // 初始化尝试次数变量
            var attemptCountVar = flowchart.GetVariable<IntegerVariable>("AttemptCount");
            if (attemptCountVar != null)
            {
                attemptCountVar.Value = 0;
            }

            // 初始化密码长度变量
            var passwordLengthVar = flowchart.GetVariable<IntegerVariable>("PasswordLength");
            if (passwordLengthVar != null)
            {
                passwordLengthVar.Value = passwordLength;
            }
        }
    }

    /// <summary>
    /// 显示密码输入界面（由Fungus Block调用）
    /// </summary>
    public void ShowPasswordInput()
    {
        if (puzzleSolved)
        {
            Debug.Log("[PasswordPuzzle] 谜题已解决");
            return;
        }

        if (passwordUI != null)
        {
            string hintText = $"请输入{passwordLength}位数字密码";
            passwordUI.ShowPasswordPanel(hintText);
        }

        Debug.Log($"[PasswordPuzzle] 显示密码输入界面: {puzzleTitle}");
    }

    /// <summary>
    /// 检查密码（由Fungus Block调用）
    /// </summary>
    public void CheckPassword()
    {
        if (flowchart == null) return;

        var userInputVar = flowchart.GetVariable<StringVariable>("UserInput");
        var correctPasswordVar = flowchart.GetVariable<StringVariable>("CorrectPassword");

        if (userInputVar == null || correctPasswordVar == null)
        {
            Debug.LogError("[PasswordPuzzle] 缺少必要的Fungus变量");
            return;
        }

        string userInput = userInputVar.Value.Trim();
        string correctPwd = correctPasswordVar.Value;

        currentAttempts++;

        // 更新尝试次数变量
        var attemptCountVar = flowchart.GetVariable<IntegerVariable>("AttemptCount");
        if (attemptCountVar != null)
        {
            attemptCountVar.Value = currentAttempts;
        }

        if (userInput == correctPwd)
        {
            // 密码正确
            OnPasswordCorrect();
        }
        else
        {
            // 密码错误
            OnPasswordWrong();
        }
    }

    /// <summary>
    /// 密码正确处理
    /// </summary>
    void OnPasswordCorrect()
    {
        puzzleSolved = true;

        // 更新Fungus变量
        if (flowchart != null)
        {
            var puzzleStateVar = flowchart.GetVariable<BooleanVariable>("PuzzleSolved");
            if (puzzleStateVar != null)
            {
                puzzleStateVar.Value = true;
            }

            // 执行密码正确的Block
            if (flowchart.HasBlock(passwordCorrectBlockName))
            {
                flowchart.ExecuteBlock(passwordCorrectBlockName);
            }
        }

        // 通知UI
        if (passwordUI != null)
        {
            passwordUI.OnPasswordCorrect();
        }

        Debug.Log($"[PasswordPuzzle] 密码正确，谜题解决: {puzzleTitle}");
    }

    /// <summary>
    /// 密码错误处理
    /// </summary>
    void OnPasswordWrong()
    {
        // 通知UI
        if (passwordUI != null)
        {
            passwordUI.OnPasswordWrong();
        }

        // 执行密码错误的Block
        if (flowchart != null && flowchart.HasBlock(passwordWrongBlockName))
        {
            flowchart.ExecuteBlock(passwordWrongBlockName);
        }

        Debug.Log($"[PasswordPuzzle] 密码错误，尝试次数: {currentAttempts}/{maxAttempts}");

        // 检查是否达到最大尝试次数
        if (currentAttempts >= maxAttempts)
        {
            OnMaxAttemptsReached();
        }
    }

    /// <summary>
    /// 达到最大尝试次数
    /// </summary>
    void OnMaxAttemptsReached()
    {
        Debug.Log($"[PasswordPuzzle] 达到最大尝试次数，谜题失败");

        // 可以在这里添加失败处理逻辑
        // 比如锁定一段时间，或者触发其他事件
    }

    /// <summary>
    /// 重置谜题状态
    /// </summary>
    public void ResetPuzzle()
    {
        puzzleSolved = false;
        currentAttempts = 0;

        // 重置Fungus变量
        if (flowchart != null)
        {
            var puzzleStateVar = flowchart.GetVariable<BooleanVariable>("PuzzleSolved");
            if (puzzleStateVar != null)
            {
                puzzleStateVar.Value = false;
            }

            var userInputVar = flowchart.GetVariable<StringVariable>("UserInput");
            if (userInputVar != null)
            {
                userInputVar.Value = "";
            }

            var attemptCountVar = flowchart.GetVariable<IntegerVariable>("AttemptCount");
            if (attemptCountVar != null)
            {
                attemptCountVar.Value = 0;
            }
        }

        Debug.Log($"[PasswordPuzzle] 谜题已重置: {puzzleTitle}");
    }

    /// <summary>
    /// 设置新密码
    /// </summary>
    public void SetPassword(string newPassword)
    {
        correctPassword = newPassword;
        passwordLength = newPassword.Length;

        if (flowchart != null)
        {
            var correctPasswordVar = flowchart.GetVariable<StringVariable>("CorrectPassword");
            if (correctPasswordVar != null)
            {
                correctPasswordVar.Value = correctPassword;
            }

            var passwordLengthVar = flowchart.GetVariable<IntegerVariable>("PasswordLength");
            if (passwordLengthVar != null)
            {
                passwordLengthVar.Value = passwordLength;
            }
        }
    }

    /// <summary>
    /// 获取谜题状态
    /// </summary>
    public bool IsPuzzleSolved()
    {
        return puzzleSolved;
    }

    /// <summary>
    /// 获取当前尝试次数
    /// </summary>
    public int GetCurrentAttempts()
    {
        return currentAttempts;
    }

    /// <summary>
    /// 检查是否还有尝试机会
    /// </summary>
    public bool HasAttemptsLeft()
    {
        return currentAttempts < maxAttempts;
    }
}
