using UnityEngine;
using Fungus;

/// <summary>
/// 密码谜题触发器
/// 当玩家与物体交互时触发密码输入界面
/// </summary>
public class PasswordPuzzleTrigger : MonoBehaviour
{
    [Header("谜题设置")]
    [SerializeField] private string puzzleTitle = "密码锁";
    [SerializeField] private string hintText = "请输入4位数字密码";
    [SerializeField] private string correctPassword = "1234";
    
    [Head<PERSON>("UI组件")]
    [SerializeField] private PasswordPuzzleUI passwordUI;
    [SerializeField] private GameObject interactionPrompt;
    
    [Header("Fungus集成")]
    [SerializeField] private Flowchart flowchart;
    [SerializeField] private string initBlockName = "InitPasswordPuzzle";
    
    [Header("交互设置")]
    [SerializeField] private KeyCode interactionKey = KeyCode.E;
    [SerializeField] private float interactionDistance = 2f;
    [SerializeField] private LayerMask playerLayer = 1;
    
    private bool playerInRange = false;
    private bool puzzleSolved = false;
    
    void Start()
    {
        SetupPuzzle();
    }
    
    void SetupPuzzle()
    {
        // 设置Fungus变量
        if (flowchart != null)
        {
            // 设置正确密码
            var correctPasswordVar = flowchart.GetVariable<StringVariable>("CorrectPassword");
            if (correctPasswordVar != null)
            {
                correctPasswordVar.Value = correctPassword;
            }
            else
            {
                Debug.LogWarning($"[PasswordPuzzle] 未找到CorrectPassword变量，请在Flowchart中创建");
            }
            
            // 初始化用户输入变量
            var userInputVar = flowchart.GetVariable<StringVariable>("UserInput");
            if (userInputVar == null)
            {
                Debug.LogWarning($"[PasswordPuzzle] 未找到UserInput变量，请在Flowchart中创建");
            }
            
            // 初始化谜题状态变量
            var puzzleStateVar = flowchart.GetVariable<BooleanVariable>("PuzzleSolved");
            if (puzzleStateVar != null)
            {
                puzzleStateVar.Value = false;
            }
        }
        
        // 隐藏交互提示
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(false);
        }
    }
    
    void Update()
    {
        CheckPlayerDistance();
        HandleInput();
    }
    
    void CheckPlayerDistance()
    {
        if (puzzleSolved) return;
        
        // 检查玩家距离
        GameObject player = GameObject.FindGameObjectWithTag("Player");
        if (player != null)
        {
            float distance = Vector3.Distance(transform.position, player.transform.position);
            bool inRange = distance <= interactionDistance;
            
            if (inRange != playerInRange)
            {
                playerInRange = inRange;
                ShowInteractionPrompt(playerInRange);
            }
        }
    }
    
    void HandleInput()
    {
        if (playerInRange && !puzzleSolved && Input.GetKeyDown(interactionKey))
        {
            StartPasswordPuzzle();
        }
    }
    
    void ShowInteractionPrompt(bool show)
    {
        if (interactionPrompt != null)
        {
            interactionPrompt.SetActive(show);
        }
    }
    
    /// <summary>
    /// 开始密码谜题
    /// </summary>
    public void StartPasswordPuzzle()
    {
        if (puzzleSolved)
        {
            Debug.Log("[PasswordPuzzle] 谜题已解决");
            return;
        }
        
        if (passwordUI != null)
        {
            passwordUI.ShowPasswordPanel(hintText);
        }
        
        // 执行Fungus初始化块
        if (flowchart != null && flowchart.HasBlock(initBlockName))
        {
            flowchart.ExecuteBlock(initBlockName);
        }
        
        Debug.Log($"[PasswordPuzzle] 开始密码谜题: {puzzleTitle}");
    }
    
    /// <summary>
    /// 谜题解决时调用（由Fungus调用）
    /// </summary>
    public void OnPuzzleSolved()
    {
        puzzleSolved = true;
        ShowInteractionPrompt(false);
        
        // 更新Fungus变量
        if (flowchart != null)
        {
            var puzzleStateVar = flowchart.GetVariable<BooleanVariable>("PuzzleSolved");
            if (puzzleStateVar != null)
            {
                puzzleStateVar.Value = true;
            }
        }
        
        Debug.Log($"[PasswordPuzzle] 谜题已解决: {puzzleTitle}");
    }
    
    /// <summary>
    /// 重置谜题状态
    /// </summary>
    public void ResetPuzzle()
    {
        puzzleSolved = false;
        
        // 重置Fungus变量
        if (flowchart != null)
        {
            var puzzleStateVar = flowchart.GetVariable<BooleanVariable>("PuzzleSolved");
            if (puzzleStateVar != null)
            {
                puzzleStateVar.Value = false;
            }
            
            var userInputVar = flowchart.GetVariable<StringVariable>("UserInput");
            if (userInputVar != null)
            {
                userInputVar.Value = "";
            }
        }
        
        Debug.Log($"[PasswordPuzzle] 谜题已重置: {puzzleTitle}");
    }
    
    /// <summary>
    /// 设置新密码
    /// </summary>
    public void SetPassword(string newPassword)
    {
        correctPassword = newPassword;
        
        if (flowchart != null)
        {
            var correctPasswordVar = flowchart.GetVariable<StringVariable>("CorrectPassword");
            if (correctPasswordVar != null)
            {
                correctPasswordVar.Value = correctPassword;
            }
        }
    }
    
    /// <summary>
    /// 获取谜题状态
    /// </summary>
    public bool IsPuzzleSolved()
    {
        return puzzleSolved;
    }
    
    void OnDrawGizmosSelected()
    {
        // 绘制交互范围
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(transform.position, interactionDistance);
    }
}
