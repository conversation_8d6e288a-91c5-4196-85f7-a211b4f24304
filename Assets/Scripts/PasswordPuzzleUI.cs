using UnityEngine;
using UnityEngine.UI;
using Fungus;
#if UNITY_2018_1_OR_NEWER
using TMPro;
#endif

/// <summary>
/// 密码谜题UI控制器
/// 与Fungus系统集成，处理密码输入界面
/// 支持Legacy InputField和TextMeshPro InputField
/// </summary>
public class PasswordPuzzleUI : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private GameObject passwordPanel;

    [Header("输入框设置 (选择其中一种)")]
    [SerializeField] private InputField legacyPasswordInput;
#if UNITY_2018_1_OR_NEWER
    [SerializeField] private TMP_InputField tmpPasswordInput;
#endif

    [SerializeField] private Button confirmButton;
    [SerializeField] private Button cancelButton;
    [SerializeField] private Text hintText;
    [SerializeField] private Text statusText;
    
    [Header("Fungus集成")]
    [SerializeField] private Flowchart flowchart;
    [SerializeField] private PasswordPuzzleManager puzzleManager;
    [SerializeField] private string checkPasswordBlockName = "CheckPassword";
    [SerializeField] private string passwordCorrectBlockName = "PasswordCorrect";
    [SerializeField] private string passwordWrongBlockName = "PasswordWrong";
    
    [Header("设置")]
    [SerializeField] private int maxAttempts = 3;
    [SerializeField] private bool clearInputOnWrong = true;
    
    private int currentAttempts = 0;

    /// <summary>
    /// 获取当前输入的文本（兼容两种InputField）
    /// </summary>
    private string InputText
    {
        get
        {
            if (legacyPasswordInput != null)
                return legacyPasswordInput.text;
#if UNITY_2018_1_OR_NEWER
            if (tmpPasswordInput != null)
                return tmpPasswordInput.text;
#endif
            return "";
        }
        set
        {
            if (legacyPasswordInput != null)
                legacyPasswordInput.text = value;
#if UNITY_2018_1_OR_NEWER
            if (tmpPasswordInput != null)
                tmpPasswordInput.text = value;
#endif
        }
    }

    /// <summary>
    /// 激活输入框（兼容两种InputField）
    /// </summary>
    private void ActivateInputField()
    {
        if (legacyPasswordInput != null)
        {
            legacyPasswordInput.Select();
            legacyPasswordInput.ActivateInputField();
        }
#if UNITY_2018_1_OR_NEWER
        else if (tmpPasswordInput != null)
        {
            tmpPasswordInput.Select();
            tmpPasswordInput.ActivateInputField();
        }
#endif
    }

    /// <summary>
    /// 检查是否有可用的输入框
    /// </summary>
    private bool HasInputField
    {
        get
        {
#if UNITY_2018_1_OR_NEWER
            return legacyPasswordInput != null || tmpPasswordInput != null;
#else
            return legacyPasswordInput != null;
#endif
        }
    }

    void Start()
    {
        SetupUI();
    }
    
    void SetupUI()
    {
        // 设置按钮事件
        if (confirmButton != null)
        {
            confirmButton.onClick.AddListener(OnConfirmPassword);
            Debug.Log("[PasswordPuzzleUI] 确认按钮事件已设置");
        }
        else
        {
            Debug.LogWarning("[PasswordPuzzleUI] 确认按钮未设置！请在Inspector中拖入Confirm Button");
        }

        if (cancelButton != null)
        {
            cancelButton.onClick.AddListener(OnCancelPassword);
            Debug.Log("[PasswordPuzzleUI] 取消按钮事件已设置");
        }
        else
        {
            Debug.LogWarning("[PasswordPuzzleUI] 取消按钮未设置");
        }

        // 设置输入框回车事件
        if (legacyPasswordInput != null)
        {
            legacyPasswordInput.onEndEdit.AddListener(OnPasswordEndEdit);
        }
#if UNITY_2018_1_OR_NEWER
        if (tmpPasswordInput != null)
        {
            tmpPasswordInput.onEndEdit.AddListener(OnPasswordEndEdit);
        }
#endif

        // 初始隐藏面板
        if (passwordPanel != null)
        {
            passwordPanel.SetActive(false);
        }
    }
    
    /// <summary>
    /// 显示密码输入面板
    /// </summary>
    public void ShowPasswordPanel(string hint = "")
    {
        if (passwordPanel != null)
        {
            passwordPanel.SetActive(true);
        }
        
        // 设置提示文本
        if (hintText != null && !string.IsNullOrEmpty(hint))
        {
            hintText.text = hint;
        }
        
        // 清空输入框和状态文本
        if (HasInputField)
        {
            InputText = "";
            ActivateInputField();
        }
        
        if (statusText != null)
        {
            statusText.text = "";
        }
        
        // 重置尝试次数
        currentAttempts = 0;
        
        // 禁用玩家移动
        if (GameManager.Instance != null)
        {
            GameManager.Instance.DisablePlayerMovement();
        }
    }
    
    /// <summary>
    /// 隐藏密码输入面板
    /// </summary>
    public void HidePasswordPanel()
    {
        if (passwordPanel != null)
        {
            passwordPanel.SetActive(false);
        }
        
        // 恢复玩家移动
        if (GameManager.Instance != null)
        {
            GameManager.Instance.EnablePlayerMovement();
        }
    }
    
    /// <summary>
    /// 确认密码按钮事件
    /// </summary>
    void OnConfirmPassword()
    {
        Debug.Log("[PasswordPuzzleUI] OnConfirmPassword 被调用");

        if (!HasInputField)
        {
            Debug.LogError("[PasswordPuzzleUI] 没有可用的输入框！请检查Legacy Password Input或Tmp Password Input是否正确设置");
            ShowStatus("输入框未正确设置", Color.red);
            return;
        }

        string inputPassword = InputText.Trim();
        Debug.Log($"[PasswordPuzzleUI] 用户输入的密码: '{inputPassword}'");

        if (string.IsNullOrEmpty(inputPassword))
        {
            Debug.LogWarning("[PasswordPuzzleUI] 密码为空");
            ShowStatus("请输入密码", Color.yellow);
            return;
        }

        // 处理密码验证 - 支持多种配置方式
        bool passwordProcessed = false;

        // 方式1：直接使用设置的Flowchart
        if (flowchart != null)
        {
            passwordProcessed = ProcessPasswordWithFlowchart(flowchart, inputPassword);
        }

        // 方式2：使用设置的PasswordPuzzleManager
        if (!passwordProcessed && puzzleManager != null)
        {
            passwordProcessed = ProcessPasswordWithManager(puzzleManager, inputPassword);
        }

        // 方式3：自动查找PasswordPuzzleManager
        if (!passwordProcessed)
        {
            var foundManager = FindObjectOfType<PasswordPuzzleManager>();
            if (foundManager != null)
            {
                passwordProcessed = ProcessPasswordWithManager(foundManager, inputPassword);
            }
        }

        // 方式4：自动查找Flowchart
        if (!passwordProcessed)
        {
            var foundFlowchart = FindObjectOfType<Flowchart>();
            if (foundFlowchart != null)
            {
                passwordProcessed = ProcessPasswordWithFlowchart(foundFlowchart, inputPassword);
            }
        }

        // 如果所有方式都失败
        if (!passwordProcessed)
        {
            Debug.LogError("[PasswordPuzzleUI] 无法找到有效的密码处理方式");
            ShowStatus("系统错误：缺少密码管理器或Flowchart", Color.red);
        }
    }

    /// <summary>
    /// 通过Flowchart处理密码
    /// </summary>
    bool ProcessPasswordWithFlowchart(Flowchart targetFlowchart, string inputPassword)
    {
        Debug.Log($"[PasswordPuzzleUI] 使用Flowchart: {targetFlowchart.name}");

        var userInputVar = targetFlowchart.GetVariable<StringVariable>("UserInput");
        if (userInputVar != null)
        {
            userInputVar.Value = inputPassword;
            Debug.Log($"[PasswordPuzzleUI] UserInput变量已设置为: '{inputPassword}'");
        }
        else
        {
            Debug.LogError($"[PasswordPuzzleUI] Flowchart '{targetFlowchart.name}' 中未找到UserInput变量");
            ShowStatus("系统错误：缺少UserInput变量", Color.red);
            return false;
        }

        // 执行密码检查块
        if (targetFlowchart.HasBlock(checkPasswordBlockName))
        {
            Debug.Log($"[PasswordPuzzleUI] 执行Block: {checkPasswordBlockName}");
            targetFlowchart.ExecuteBlock(checkPasswordBlockName);
            return true;
        }
        else
        {
            Debug.LogError($"[PasswordPuzzleUI] Flowchart '{targetFlowchart.name}' 中未找到Block: '{checkPasswordBlockName}'");
            ShowStatus($"系统错误：缺少{checkPasswordBlockName}块", Color.red);
            return false;
        }
    }

    /// <summary>
    /// 通过PasswordPuzzleManager处理密码
    /// </summary>
    bool ProcessPasswordWithManager(PasswordPuzzleManager manager, string inputPassword)
    {
        Debug.Log($"[PasswordPuzzleUI] 使用PasswordPuzzleManager: {manager.name}");

        // 首先尝试从Manager获取Flowchart
        var managerFlowchart = manager.GetComponent<Flowchart>();
        if (managerFlowchart != null)
        {
            // Manager有自己的Flowchart
            var userInputVar = managerFlowchart.GetVariable<StringVariable>("UserInput");
            if (userInputVar != null)
            {
                userInputVar.Value = inputPassword;
                Debug.Log($"[PasswordPuzzleUI] 通过Manager的Flowchart设置UserInput: '{inputPassword}'");
                manager.CheckPassword();
                return true;
            }
            else
            {
                Debug.LogError($"[PasswordPuzzleUI] Manager的Flowchart中未找到UserInput变量");
                ShowStatus("系统错误：Manager的Flowchart缺少UserInput变量", Color.red);
                return false;
            }
        }
        else
        {
            // Manager没有Flowchart，尝试使用场景中的其他Flowchart
            Debug.Log("[PasswordPuzzleUI] Manager没有Flowchart，尝试使用其他Flowchart");
            var sceneFlowchart = FindObjectOfType<Flowchart>();
            if (sceneFlowchart != null)
            {
                var userInputVar = sceneFlowchart.GetVariable<StringVariable>("UserInput");
                if (userInputVar != null)
                {
                    userInputVar.Value = inputPassword;
                    Debug.Log($"[PasswordPuzzleUI] 通过场景Flowchart设置UserInput: '{inputPassword}'");
                    manager.CheckPassword();
                    return true;
                }
                else
                {
                    Debug.LogError("[PasswordPuzzleUI] 场景Flowchart中未找到UserInput变量");
                    ShowStatus("系统错误：Flowchart缺少UserInput变量", Color.red);
                    return false;
                }
            }
            else
            {
                Debug.LogError("[PasswordPuzzleUI] 既没有Manager的Flowchart也没有场景Flowchart");
                ShowStatus("系统错误：缺少Flowchart", Color.red);
                return false;
            }
        }
    }

    /// <summary>
    /// 取消密码输入
    /// </summary>
    void OnCancelPassword()
    {
        HidePasswordPanel();
    }
    
    /// <summary>
    /// 输入框回车事件
    /// </summary>
    void OnPasswordEndEdit(string value)
    {
        if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
        {
            OnConfirmPassword();
        }
    }
    
    /// <summary>
    /// 密码正确时调用（由Fungus调用）
    /// </summary>
    public void OnPasswordCorrect()
    {
        ShowStatus("密码正确！", Color.green);
        
        // 延迟隐藏面板
        Invoke(nameof(HidePasswordPanel), 1.5f);
    }
    
    /// <summary>
    /// 密码错误时调用（由Fungus调用）
    /// </summary>
    public void OnPasswordWrong()
    {
        currentAttempts++;
        
        if (currentAttempts >= maxAttempts)
        {
            ShowStatus($"密码错误！已达到最大尝试次数({maxAttempts})", Color.red);
            
            // 延迟隐藏面板
            Invoke(nameof(HidePasswordPanel), 2f);
        }
        else
        {
            int remainingAttempts = maxAttempts - currentAttempts;
            ShowStatus($"密码错误！还有{remainingAttempts}次机会", Color.red);
            
            if (clearInputOnWrong && HasInputField)
            {
                InputText = "";
                ActivateInputField();
            }
        }
    }
    
    /// <summary>
    /// 显示状态信息
    /// </summary>
    void ShowStatus(string message, Color color)
    {
        if (statusText != null)
        {
            statusText.text = message;
            statusText.color = color;
        }
        
        Debug.Log($"[PasswordPuzzle] {message}");
    }
    
    /// <summary>
    /// 设置提示文本
    /// </summary>
    public void SetHint(string hint)
    {
        if (hintText != null)
        {
            hintText.text = hint;
        }
    }
    
    /// <summary>
    /// 获取当前尝试次数
    /// </summary>
    public int GetCurrentAttempts()
    {
        return currentAttempts;
    }
    
    /// <summary>
    /// 检查是否还有尝试机会
    /// </summary>
    public bool HasAttemptsLeft()
    {
        return currentAttempts < maxAttempts;
    }
}
