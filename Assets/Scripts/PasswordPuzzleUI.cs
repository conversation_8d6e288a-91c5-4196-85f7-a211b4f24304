using UnityEngine;
using UnityEngine.UI;
using Fungus;
#if UNITY_2018_1_OR_NEWER
using TMPro;
#endif

/// <summary>
/// 密码谜题UI控制器
/// 与Fungus系统集成，处理密码输入界面
/// 支持Legacy InputField和TextMeshPro InputField
/// </summary>
public class PasswordPuzzleUI : MonoBehaviour
{
    [Header("UI组件")]
    [SerializeField] private GameObject passwordPanel;

    [Header("输入框设置 (选择其中一种)")]
    [SerializeField] private InputField legacyPasswordInput;
#if UNITY_2018_1_OR_NEWER
    [SerializeField] private TMP_InputField tmpPasswordInput;
#endif

    [SerializeField] private Button confirmButton;
    [SerializeField] private Button cancelButton;
    [SerializeField] private Text hintText;
    [SerializeField] private Text statusText;
    
    [Header("Fungus集成")]
    [SerializeField] private Flowchart flowchart;
    [SerializeField] private string checkPasswordBlockName = "CheckPassword";
    [SerializeField] private string passwordCorrectBlockName = "PasswordCorrect";
    [SerializeField] private string passwordWrongBlockName = "PasswordWrong";
    
    [Header("设置")]
    [SerializeField] private int maxAttempts = 3;
    [SerializeField] private bool clearInputOnWrong = true;
    
    private int currentAttempts = 0;

    /// <summary>
    /// 获取当前输入的文本（兼容两种InputField）
    /// </summary>
    private string InputText
    {
        get
        {
            if (legacyPasswordInput != null)
                return legacyPasswordInput.text;
#if UNITY_2018_1_OR_NEWER
            if (tmpPasswordInput != null)
                return tmpPasswordInput.text;
#endif
            return "";
        }
        set
        {
            if (legacyPasswordInput != null)
                legacyPasswordInput.text = value;
#if UNITY_2018_1_OR_NEWER
            if (tmpPasswordInput != null)
                tmpPasswordInput.text = value;
#endif
        }
    }

    /// <summary>
    /// 激活输入框（兼容两种InputField）
    /// </summary>
    private void ActivateInputField()
    {
        if (legacyPasswordInput != null)
        {
            legacyPasswordInput.Select();
            legacyPasswordInput.ActivateInputField();
        }
#if UNITY_2018_1_OR_NEWER
        else if (tmpPasswordInput != null)
        {
            tmpPasswordInput.Select();
            tmpPasswordInput.ActivateInputField();
        }
#endif
    }

    /// <summary>
    /// 检查是否有可用的输入框
    /// </summary>
    private bool HasInputField
    {
        get
        {
#if UNITY_2018_1_OR_NEWER
            return legacyPasswordInput != null || tmpPasswordInput != null;
#else
            return legacyPasswordInput != null;
#endif
        }
    }

    void Start()
    {
        SetupUI();
    }
    
    void SetupUI()
    {
        // 设置按钮事件
        if (confirmButton != null)
        {
            confirmButton.onClick.AddListener(OnConfirmPassword);
        }

        if (cancelButton != null)
        {
            cancelButton.onClick.AddListener(OnCancelPassword);
        }

        // 设置输入框回车事件
        if (legacyPasswordInput != null)
        {
            legacyPasswordInput.onEndEdit.AddListener(OnPasswordEndEdit);
        }
#if UNITY_2018_1_OR_NEWER
        if (tmpPasswordInput != null)
        {
            tmpPasswordInput.onEndEdit.AddListener(OnPasswordEndEdit);
        }
#endif

        // 初始隐藏面板
        if (passwordPanel != null)
        {
            passwordPanel.SetActive(false);
        }
    }
    
    /// <summary>
    /// 显示密码输入面板
    /// </summary>
    public void ShowPasswordPanel(string hint = "")
    {
        if (passwordPanel != null)
        {
            passwordPanel.SetActive(true);
        }
        
        // 设置提示文本
        if (hintText != null && !string.IsNullOrEmpty(hint))
        {
            hintText.text = hint;
        }
        
        // 清空输入框和状态文本
        if (HasInputField)
        {
            InputText = "";
            ActivateInputField();
        }
        
        if (statusText != null)
        {
            statusText.text = "";
        }
        
        // 重置尝试次数
        currentAttempts = 0;
        
        // 禁用玩家移动
        if (GameManager.Instance != null)
        {
            GameManager.Instance.DisablePlayerMovement();
        }
    }
    
    /// <summary>
    /// 隐藏密码输入面板
    /// </summary>
    public void HidePasswordPanel()
    {
        if (passwordPanel != null)
        {
            passwordPanel.SetActive(false);
        }
        
        // 恢复玩家移动
        if (GameManager.Instance != null)
        {
            GameManager.Instance.EnablePlayerMovement();
        }
    }
    
    /// <summary>
    /// 确认密码按钮事件
    /// </summary>
    void OnConfirmPassword()
    {
        if (HasInputField)
        {
            string inputPassword = InputText.Trim();

            if (string.IsNullOrEmpty(inputPassword))
            {
                ShowStatus("请输入密码", Color.yellow);
                return;
            }

            // 将用户输入存储到Fungus变量中
            if (flowchart != null)
            {
                var userInputVar = flowchart.GetVariable<StringVariable>("UserInput");
                if (userInputVar != null)
                {
                    userInputVar.Value = inputPassword;
                }

                // 执行密码检查块
                if (flowchart.HasBlock(checkPasswordBlockName))
                {
                    flowchart.ExecuteBlock(checkPasswordBlockName);
                }
            }
            else
            {
                // 如果没有flowchart，尝试找到PasswordPuzzleManager
                var puzzleManager = FindObjectOfType<PasswordPuzzleManager>();
                if (puzzleManager != null)
                {
                    // 通过PasswordPuzzleManager处理密码检查
                    var userInputVar = puzzleManager.GetComponent<Flowchart>()?.GetVariable<StringVariable>("UserInput");
                    if (userInputVar != null)
                    {
                        userInputVar.Value = inputPassword;
                        puzzleManager.CheckPassword();
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// 取消密码输入
    /// </summary>
    void OnCancelPassword()
    {
        HidePasswordPanel();
    }
    
    /// <summary>
    /// 输入框回车事件
    /// </summary>
    void OnPasswordEndEdit(string value)
    {
        if (Input.GetKeyDown(KeyCode.Return) || Input.GetKeyDown(KeyCode.KeypadEnter))
        {
            OnConfirmPassword();
        }
    }
    
    /// <summary>
    /// 密码正确时调用（由Fungus调用）
    /// </summary>
    public void OnPasswordCorrect()
    {
        ShowStatus("密码正确！", Color.green);
        
        // 延迟隐藏面板
        Invoke(nameof(HidePasswordPanel), 1.5f);
    }
    
    /// <summary>
    /// 密码错误时调用（由Fungus调用）
    /// </summary>
    public void OnPasswordWrong()
    {
        currentAttempts++;
        
        if (currentAttempts >= maxAttempts)
        {
            ShowStatus($"密码错误！已达到最大尝试次数({maxAttempts})", Color.red);
            
            // 延迟隐藏面板
            Invoke(nameof(HidePasswordPanel), 2f);
        }
        else
        {
            int remainingAttempts = maxAttempts - currentAttempts;
            ShowStatus($"密码错误！还有{remainingAttempts}次机会", Color.red);
            
            if (clearInputOnWrong && HasInputField)
            {
                InputText = "";
                ActivateInputField();
            }
        }
    }
    
    /// <summary>
    /// 显示状态信息
    /// </summary>
    void ShowStatus(string message, Color color)
    {
        if (statusText != null)
        {
            statusText.text = message;
            statusText.color = color;
        }
        
        Debug.Log($"[PasswordPuzzle] {message}");
    }
    
    /// <summary>
    /// 设置提示文本
    /// </summary>
    public void SetHint(string hint)
    {
        if (hintText != null)
        {
            hintText.text = hint;
        }
    }
    
    /// <summary>
    /// 获取当前尝试次数
    /// </summary>
    public int GetCurrentAttempts()
    {
        return currentAttempts;
    }
    
    /// <summary>
    /// 检查是否还有尝试机会
    /// </summary>
    public bool HasAttemptsLeft()
    {
        return currentAttempts < maxAttempts;
    }
}
