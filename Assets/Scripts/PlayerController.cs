using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [Header("移动设置")]
    public float moveSpeed = 5f;
    public float leftBoundary = -10f;
    public float rightBoundary = 10f;

    [Header("动画设置")]
    public Animator animator;
    public string walkAnimationName = "walk";
    public string idleAnimationName = "Idle";
    public bool flipSpriteOnDirection = true;

    [Header("状态")]
    private bool canMove = true;
    public bool HasKey = false;

    private bool isMoving = false;
    private SpriteRenderer spriteRenderer;

    void Start()
    {
        // 获取组件引用
        if (animator == null)
        {
            animator = GetComponent<Animator>();
        }

        if (flipSpriteOnDirection)
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
        }

        // 检查必要组件
        if (animator == null)
        {
            Debug.LogWarning($"[PlayerController] {gameObject.name} 没有找到Animator组件！");
        }

        if (flipSpriteOnDirection && spriteRenderer == null)
        {
            Debug.LogWarning($"[PlayerController] {gameObject.name} 没有找到SpriteRenderer组件！");
        }
    }

    void Update()
    {
        if (!canMove)
        {
            // 如果不能移动，播放待机动画
            SetMovingState(false);
            return;
        }

        float horizontalInput = Input.GetAxis("Horizontal");
        bool isCurrentlyMoving = Mathf.Abs(horizontalInput) > 0.1f;

        // 处理移动
        if (isCurrentlyMoving)
        {
            Vector3 movement = new Vector3(horizontalInput, 0, 0) * moveSpeed * Time.deltaTime;

            // 检查边界限制
            Vector3 newPosition = transform.position + movement;
            if (newPosition.x >= leftBoundary && newPosition.x <= rightBoundary)
            {
                transform.position = newPosition;

                // 处理精灵翻转
                if (flipSpriteOnDirection && spriteRenderer != null)
                {
                    if (horizontalInput > 0)
                    {
                        spriteRenderer.flipX = false; // 向右移动，不翻转
                    }
                    else if (horizontalInput < 0)
                    {
                        spriteRenderer.flipX = true;  // 向左移动，翻转
                    }
                }
            }
        }

        // 更新动画状态
        SetMovingState(isCurrentlyMoving);
    }

    /// <summary>
    /// 设置移动状态和对应的动画
    /// </summary>
    void SetMovingState(bool moving)
    {
        if (isMoving != moving)
        {
            isMoving = moving;

            if (animator != null)
            {
                if (isMoving)
                {
                    // 播放行走动画
                    if (!string.IsNullOrEmpty(walkAnimationName))
                    {
                        animator.Play(walkAnimationName);
                    }
                }
                else
                {
                    // 播放待机动画
                    if (!string.IsNullOrEmpty(idleAnimationName))
                    {
                        animator.Play(idleAnimationName);
                    }
                }
            }
        }
    }

    public void GetKey()
    {
        HasKey = true;
    }

    /// <summary>
    /// 启用玩家移动
    /// </summary>
    public void EnableMovement()
    {
        canMove = true;
    }

    /// <summary>
    /// 禁用玩家移动
    /// </summary>
    public void DisableMovement()
    {
        canMove = false;
        SetMovingState(false); // 停止移动时播放待机动画
    }

    /// <summary>
    /// 手动播放指定动画
    /// </summary>
    public void PlayAnimation(string animationName)
    {
        if (animator != null && !string.IsNullOrEmpty(animationName))
        {
            animator.Play(animationName);
        }
    }

    /// <summary>
    /// 检查玩家是否正在移动
    /// </summary>
    public bool IsMoving()
    {
        return isMoving;
    }

    /// <summary>
    /// 设置移动速度
    /// </summary>
    public void SetMoveSpeed(float newSpeed)
    {
        moveSpeed = newSpeed;
    }

    /// <summary>
    /// 获取当前移动速度
    /// </summary>
    public float GetMoveSpeed()
    {
        return moveSpeed;
    }
}
