using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

public class PlayerController : MonoBehaviour
{
    [SerializeField] private Transform[] SceneTransforms;
    private int m_SceneIndex = 0;
    [Header("移动设置")]
    public float moveSpeed = 5f;
    public float leftBoundary = -10f;
    public float rightBoundary = 10f;

    [Header("动画设置")]
    public Animator animator;
    public string walkAnimationName = "walk";
    public string idleAnimationName = "idle";
    public bool flipSpriteOnDirection = true;
    public bool useAnimatorParameters = true; // 使用Animator参数而不是直接播放状态

    [Header("状态")]
    private bool canMove = true;
    public bool HasKey = false;
    private Rigidbody2D rb;

    private bool isMoving = false;
    private SpriteRenderer spriteRenderer;

    void Start()
    {
        rb = GetComponent<Rigidbody2D>();
        // 获取组件引用
        if (animator == null)
        {
            animator = GetComponent<Animator>();
        }

        if (flipSpriteOnDirection)
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
        }

        // 检查必要组件
        if (animator == null)
        {
            Debug.LogWarning($"[PlayerController] {gameObject.name} 没有找到Animator组件！");
        }

        if (flipSpriteOnDirection && spriteRenderer == null)
        {
            Debug.LogWarning($"[PlayerController] {gameObject.name} 没有找到SpriteRenderer组件！");
        }
    }

    void Update()
    {
        if (!canMove)
        {
            // 如果不能移动，播放待机动画
            SetMovingState(false);
            return;
        }

        float horizontalInput = Input.GetAxis("Horizontal");

        // 使用 Rigidbody2D 的 velocity 来移动
        rb.velocity = new Vector2(horizontalInput * moveSpeed, rb.velocity.y);

        // 检查边界限制
        // Vector3 currentPosition = transform.position;
        // if (currentPosition.x < leftBoundary || currentPosition.x > rightBoundary)
        // {
        //     // 限制位置并停止水平移动
        //     float clampedX = Mathf.Clamp(currentPosition.x, leftBoundary, rightBoundary);
        //     transform.position = new Vector3(clampedX, currentPosition.y, currentPosition.z);
        //     rb.velocity = new Vector2(0, rb.velocity.y);
        // }
        bool isCurrentlyMoving = Mathf.Abs(horizontalInput) > 0.1f;

        if (isCurrentlyMoving)
        {
            // 处理精灵翻转
            if (flipSpriteOnDirection && spriteRenderer != null)
            {
                if (horizontalInput > 0)
                {
                    spriteRenderer.flipX = false; // 向右移动，不翻转
                }
                else if (horizontalInput < 0)
                {
                    spriteRenderer.flipX = true;  // 向左移动，翻转
                }
            }
        }

        // 更新动画状态
        SetMovingState(isCurrentlyMoving);
    }

    /// <summary>
    /// 设置移动状态和对应的动画
    /// </summary>
    void SetMovingState(bool moving)
    {
        if (isMoving != moving)
        {
            isMoving = moving;

            if (animator != null)
            {
                if (useAnimatorParameters)
                {
                    // 使用Animator参数控制动画（推荐方式）
                    animator.SetBool("IsWalking", isMoving);
                    animator.SetBool("IsMoving", isMoving);
                    animator.SetFloat("Speed", isMoving ? 1f : 0f);
                }
                else
                {
                    // 直接播放动画状态
                    if (isMoving)
                    {
                        // 播放行走动画
                        if (!string.IsNullOrEmpty(walkAnimationName))
                        {
                            animator.Play(walkAnimationName, 0, 0f); // 从头开始播放
                        }
                    }
                    else
                    {
                        // 播放待机动画
                        if (!string.IsNullOrEmpty(idleAnimationName))
                        {
                            animator.Play(idleAnimationName, 0, 0f); // 从头开始播放
                        }
                    }
                }
            }
        }
    }

    public void GetKey()
    {
        HasKey = true;
    }

    public void EnableMovement() => canMove = true;

    public void DisableMovement()
    {
        canMove = false;
        rb.velocity = Vector2.zero; // 停止移动时清零速度
        SetMovingState(false); // 停止移动时播放待机动画
    }

    public void SetPlayerTransform(int playerTransIndex)
    {
        // 检查数组是否为空和索引是否有效
        if (SceneTransforms == null || SceneTransforms.Length == 0)
        {
            Debug.LogWarning("SceneTransforms array is null or empty!");
            return;
        }

        // 确保索引在有效范围内
        if (playerTransIndex < SceneTransforms.Length)
        {
            rb.velocity = Vector2.zero;
            rb.angularVelocity = 0f;
            
            // 使用 Rigidbody2D 来设置位置，避免物理冲突
            Vector3 targetPosition = SceneTransforms[playerTransIndex].position;
            rb.position = new Vector2(targetPosition.x, targetPosition.y);
            
            // 清零速度，避免传送后继续移动
            rb.velocity = Vector2.zero;
            
            // Debug.Log($"Player teleported to scene {m_SceneIndex} at position {targetPosition}");
            
            // m_SceneIndex++;
        }
        else
        {
            // Debug.LogWarning($"Scene index {m_SceneIndex} is out of bounds! Max index: {SceneTransforms.Length - 1}");
        }
    }

    /// <summary>
    /// 手动播放指定动画
    /// </summary>
    public void PlayAnimation(string animationName)
    {
        if (animator != null && !string.IsNullOrEmpty(animationName))
        {
            animator.Play(animationName);
        }
    }

    /// <summary>
    /// 检查玩家是否正在移动
    /// </summary>
    public bool IsMoving()
    {
        return isMoving;
    }

    /// <summary>
    /// 设置移动速度
    /// </summary>
    public void SetMoveSpeed(float newSpeed)
    {
        moveSpeed = newSpeed;
    }

    /// <summary>
    /// 获取当前移动速度
    /// </summary>
    public float GetMoveSpeed()
    {
        return moveSpeed;
    }
}
