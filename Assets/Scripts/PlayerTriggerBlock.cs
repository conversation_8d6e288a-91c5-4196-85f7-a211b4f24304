using UnityEngine;
using Fungus;

/// <summary>
/// 玩家触发器脚本
/// 当玩家进入触发区域时自动执行指定的Fungus Block
/// </summary>
public class PlayerTriggerBlock : MonoBehaviour
{
    [Header("基本设置")]
    [SerializeField] private Flowchart flowchart;
    public string blockName = "PlayerEnter";
    [SerializeField] private bool triggerOnlyOnce = false;

    private bool hasTriggered = false;

    void OnTriggerEnter2D(Collider2D other)
    {
        if (other.CompareTag("Player"))
        {
            TriggerBlock();
        }
    }

    void OnTriggerEnter(Collider other)
    {
        if (other.CompareTag("Player"))
        {
            TriggerBlock();
        }
    }

    void TriggerBlock()
    {
        // 检查是否只触发一次
        if (triggerOnlyOnce && hasTriggered)
        {
            return;
        }

        // 检查配置
        if (flowchart == null || string.IsNullOrEmpty(blockName))
        {
            Debug.LogError($"[PlayerTriggerBlock] {gameObject.name} 配置不完整");
            return;
        }

        // 执行Block
        if (flowchart.HasBlock(blockName))
        {
            Debug.Log($"[PlayerTriggerBlock] 触发Block: {blockName}");
            flowchart.ExecuteBlock(blockName);
            hasTriggered = true;
        }
        else
        {
            Debug.LogError($"[PlayerTriggerBlock] 未找到Block: {blockName}");
        }
    }

    /// <summary>
    /// 重置触发状态
    /// </summary>
    [ContextMenu("重置触发状态")]
    public void ResetTrigger()
    {
        hasTriggered = false;
        Debug.Log($"[PlayerTriggerBlock] {gameObject.name} 触发状态已重置");
    }
}
