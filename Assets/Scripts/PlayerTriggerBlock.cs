using UnityEngine;
using Fungus;

/// <summary>
/// 玩家触发器脚本
/// 当玩家进入触发区域时自动执行指定的Fungus Block
/// </summary>
public class PlayerTriggerBlock : MonoBehaviour
{
    [Header("Fungus设置")]
    [SerializeField] private Flowchart flowchart;
    [SerializeField] private string blockName = "PlayerEnter";
    
    [Header("触发设置")]
    [SerializeField] private bool triggerOnEnter = true;
    [SerializeField] private bool triggerOnExit = false;
    [SerializeField] private bool triggerOnlyOnce = false;
    [SerializeField] private bool requirePlayerTag = true;
    [SerializeField] private string playerTag = "Player";
    
    [Header("延迟设置")]
    [SerializeField] private float triggerDelay = 0f;
    
    [Header("调试信息")]
    [SerializeField] private bool enableDebugLog = true;
    [SerializeField] private bool showGizmos = true;
    
    private bool hasTriggered = false;
    private bool playerInside = false;
    
    void Start()
    {
        // 确保有Collider组件且设置为Trigger
        var collider = GetComponent<Collider>();
        var collider2D = GetComponent<Collider2D>();
        
        if (collider == null && collider2D == null)
        {
            Debug.LogError($"[PlayerTriggerBlock] {gameObject.name} 需要Collider或Collider2D组件！");
            return;
        }
        
        if (collider != null && !collider.isTrigger)
        {
            Debug.LogWarning($"[PlayerTriggerBlock] {gameObject.name} 的Collider未设置为Trigger，已自动设置");
            collider.isTrigger = true;
        }
        
        if (collider2D != null && !collider2D.isTrigger)
        {
            Debug.LogWarning($"[PlayerTriggerBlock] {gameObject.name} 的Collider2D未设置为Trigger，已自动设置");
            collider2D.isTrigger = true;
        }
        
        // 检查Flowchart和Block
        ValidateSettings();
    }
    
    void ValidateSettings()
    {
        if (flowchart == null)
        {
            Debug.LogError($"[PlayerTriggerBlock] {gameObject.name} 的Flowchart未设置！");
            return;
        }
        
        if (string.IsNullOrEmpty(blockName))
        {
            Debug.LogError($"[PlayerTriggerBlock] {gameObject.name} 的Block Name未设置！");
            return;
        }
        
        if (!flowchart.HasBlock(blockName))
        {
            Debug.LogError($"[PlayerTriggerBlock] Flowchart中未找到Block: '{blockName}'");
            return;
        }
        
        if (enableDebugLog)
        {
            Debug.Log($"[PlayerTriggerBlock] {gameObject.name} 初始化完成，监听Block: '{blockName}'");
        }
    }
    
    #region 3D Collider Events
    void OnTriggerEnter(Collider other)
    {
        HandleTriggerEnter(other.gameObject);
    }
    
    void OnTriggerExit(Collider other)
    {
        HandleTriggerExit(other.gameObject);
    }
    #endregion
    
    #region 2D Collider Events
    void OnTriggerEnter2D(Collider2D other)
    {
        HandleTriggerEnter(other.gameObject);
    }
    
    void OnTriggerExit2D(Collider2D other)
    {
        HandleTriggerExit(other.gameObject);
    }
    #endregion
    
    void HandleTriggerEnter(GameObject other)
    {
        if (!IsValidPlayer(other)) return;
        
        playerInside = true;
        
        if (enableDebugLog)
        {
            Debug.Log($"[PlayerTriggerBlock] 玩家进入触发区域: {gameObject.name}");
        }
        
        if (triggerOnEnter)
        {
            TriggerBlock("Enter");
        }
    }
    
    void HandleTriggerExit(GameObject other)
    {
        if (!IsValidPlayer(other)) return;
        
        playerInside = false;
        
        if (enableDebugLog)
        {
            Debug.Log($"[PlayerTriggerBlock] 玩家离开触发区域: {gameObject.name}");
        }
        
        if (triggerOnExit)
        {
            TriggerBlock("Exit");
        }
    }
    
    bool IsValidPlayer(GameObject obj)
    {
        if (!requirePlayerTag) return true;
        
        return obj.CompareTag(playerTag);
    }
    
    void TriggerBlock(string eventType)
    {
        // 检查是否只触发一次
        if (triggerOnlyOnce && hasTriggered)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[PlayerTriggerBlock] {gameObject.name} 已触发过，跳过");
            }
            return;
        }
        
        // 检查组件状态
        if (flowchart == null || string.IsNullOrEmpty(blockName))
        {
            Debug.LogError($"[PlayerTriggerBlock] {gameObject.name} 配置不完整，无法触发Block");
            return;
        }
        
        if (!flowchart.HasBlock(blockName))
        {
            Debug.LogError($"[PlayerTriggerBlock] Flowchart中未找到Block: '{blockName}'");
            return;
        }
        
        // 执行触发
        if (triggerDelay > 0)
        {
            if (enableDebugLog)
            {
                Debug.Log($"[PlayerTriggerBlock] {triggerDelay}秒后触发Block: '{blockName}' ({eventType})");
            }
            Invoke(nameof(ExecuteBlock), triggerDelay);
        }
        else
        {
            ExecuteBlock();
        }
        
        hasTriggered = true;
    }
    
    void ExecuteBlock()
    {
        if (flowchart != null && flowchart.HasBlock(blockName))
        {
            if (enableDebugLog)
            {
                Debug.Log($"[PlayerTriggerBlock] 执行Block: '{blockName}'");
            }
            
            flowchart.ExecuteBlock(blockName);
        }
    }
    
    /// <summary>
    /// 重置触发状态（用于调试或特殊需求）
    /// </summary>
    [ContextMenu("重置触发状态")]
    public void ResetTrigger()
    {
        hasTriggered = false;
        playerInside = false;
        
        if (enableDebugLog)
        {
            Debug.Log($"[PlayerTriggerBlock] {gameObject.name} 触发状态已重置");
        }
    }
    
    /// <summary>
    /// 手动触发Block（用于测试）
    /// </summary>
    [ContextMenu("手动触发Block")]
    public void ManualTrigger()
    {
        TriggerBlock("Manual");
    }
    
    /// <summary>
    /// 设置新的Block名称
    /// </summary>
    public void SetBlockName(string newBlockName)
    {
        blockName = newBlockName;
        ValidateSettings();
    }
    
    /// <summary>
    /// 设置新的Flowchart
    /// </summary>
    public void SetFlowchart(Flowchart newFlowchart)
    {
        flowchart = newFlowchart;
        ValidateSettings();
    }
    
    /// <summary>
    /// 检查玩家是否在触发区域内
    /// </summary>
    public bool IsPlayerInside()
    {
        return playerInside;
    }
    
    /// <summary>
    /// 检查是否已经触发过
    /// </summary>
    public bool HasTriggered()
    {
        return hasTriggered;
    }
    
    #region Gizmos
    void OnDrawGizmos()
    {
        if (!showGizmos) return;
        
        // 绘制触发区域
        Gizmos.color = playerInside ? Color.green : Color.yellow;
        Gizmos.color = new Color(Gizmos.color.r, Gizmos.color.g, Gizmos.color.b, 0.3f);
        
        var collider = GetComponent<Collider>();
        var collider2D = GetComponent<Collider2D>();
        
        if (collider != null)
        {
            if (collider is BoxCollider box)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawCube(box.center, box.size);
            }
            else if (collider is SphereCollider sphere)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawSphere(sphere.center, sphere.radius);
            }
        }
        else if (collider2D != null)
        {
            if (collider2D is BoxCollider2D box2D)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawCube(box2D.offset, box2D.size);
            }
            else if (collider2D is CircleCollider2D circle2D)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawSphere(circle2D.offset, circle2D.radius);
            }
        }
        
        // 绘制状态指示
        Gizmos.color = hasTriggered ? Color.red : Color.white;
        Gizmos.DrawWireCube(transform.position + Vector3.up * 2, Vector3.one * 0.5f);
    }
    
    void OnDrawGizmosSelected()
    {
        if (!showGizmos) return;
        
        // 绘制触发区域边框
        Gizmos.color = Color.cyan;
        
        var collider = GetComponent<Collider>();
        var collider2D = GetComponent<Collider2D>();
        
        if (collider != null)
        {
            if (collider is BoxCollider box)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawWireCube(box.center, box.size);
            }
            else if (collider is SphereCollider sphere)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawWireSphere(sphere.center, sphere.radius);
            }
        }
        else if (collider2D != null)
        {
            if (collider2D is BoxCollider2D box2D)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawWireCube(box2D.offset, box2D.size);
            }
            else if (collider2D is CircleCollider2D circle2D)
            {
                Gizmos.matrix = transform.localToWorldMatrix;
                Gizmos.DrawWireSphere(circle2D.offset, circle2D.radius);
            }
        }
    }
    #endregion
}
