using System.Collections;
using System.Collections.Generic;
using Fungus;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.Video;

public class VideoController : MonoBehaviour
{
    [Header("视频播放器设置")]
    public VideoPlayer videoPlayer; // 拖入你的VideoPlayer组件
    public RawImage videoScreen; // 拖入RawImage

    [Header("视频片段设置")]
    public List<VideoClip> videoClips = new List<VideoClip>(); // 视频片段列表

    [Header("调试信息")]
    public bool enableDebugLog = true;

    public void ShowVideoScreen() => videoScreen.gameObject.SetActive(true);
    public void HideVideoScreen() => videoScreen.gameObject.SetActive(false);

    /// <summary>
    /// 播放指定索引的视频
    /// </summary>
    public void PlayVideo(int videoIndex)
    {
        if (videoIndex < 0 || videoIndex >= videoClips.Count)
        {
            Debug.LogError($"[VideoController] 视频索引 {videoIndex} 超出范围！总共有 {videoClips.Count} 个视频");
            return;
        }

        if (videoClips[videoIndex] == null)
        {
            Debug.LogError($"[VideoController] 视频索引 {videoIndex} 的VideoClip为空！");
            return;
        }

        videoPlayer.clip = videoClips[videoIndex];
        videoPlayer.Play();

        if (enableDebugLog)
        {
            Debug.Log($"[VideoController] 开始播放视频 {videoIndex}: {videoClips[videoIndex].name}");
        }
    }

    /// <summary>
    /// 播放第一个视频（兼容旧版本）
    /// </summary>
    public void PlayVideo()
    {
        PlayVideo(0);
    }

    /// <summary>
    /// 播放第一段视频
    /// </summary>
    public void PlayFirstVideo()
    {
        PlayVideo(0);
    }

    /// <summary>
    /// 播放第二段视频
    /// </summary>
    public void PlaySecondVideo()
    {
        PlayVideo(1);
    }

    // 暂停视频
    public void PauseVideo()
    {
        videoPlayer.Pause();

        if (enableDebugLog)
        {
            Debug.Log("[VideoController] 视频已暂停");
        }
    }

    // 停止视频
    public void StopVideo()
    {
        videoPlayer.Stop();

        if (enableDebugLog)
        {
            Debug.Log("[VideoController] 视频已停止");
        }
    }

    // 检查是否播放完毕（用于等待结束）
    public bool IsVideoFinished()
    {
        return !videoPlayer.isPlaying;
    }

    /// <summary>
    /// 播放指定视频并等待完成后执行Block
    /// </summary>
    public void PlayVideoAndWait(int videoIndex, Block blockToExecute)
    {
        StartCoroutine(PlayAndWait(videoIndex, blockToExecute));
    }

    /// <summary>
    /// 播放第一个视频并等待（兼容旧版本）
    /// </summary>
    public void PlayVideoAndWait(Block blockToExecute)
    {
        PlayVideoAndWait(0, blockToExecute);
    }

    /// <summary>
    /// 播放第一段视频并等待完成
    /// </summary>
    public void PlayFirstVideoAndWait(Block blockToExecute)
    {
        PlayVideoAndWait(0, blockToExecute);
    }

    /// <summary>
    /// 播放第二段视频并等待完成
    /// </summary>
    public void PlaySecondVideoAndWait(Block blockToExecute)
    {
        PlayVideoAndWait(1, blockToExecute);
    }

    private IEnumerator PlayAndWait(int videoIndex, Block targetBlock)
    {
        // 播放指定视频
        PlayVideo(videoIndex);

        // 等待视频播放完毕
        while (videoPlayer.isPlaying)
        {
            yield return null;
        }

        if (enableDebugLog)
        {
            Debug.Log($"[VideoController] 视频 {videoIndex} 播放完成，执行下一个Block");
        }

        // 执行下一个Block
        if (targetBlock != null)
        {
            targetBlock.StartExecution();
        }
    }

    /// <summary>
    /// 获取当前播放的视频索引
    /// </summary>
    public int GetCurrentVideoIndex()
    {
        if (videoPlayer.clip == null) return -1;

        for (int i = 0; i < videoClips.Count; i++)
        {
            if (videoClips[i] == videoPlayer.clip)
            {
                return i;
            }
        }
        return -1;
    }

    /// <summary>
    /// 获取视频总数
    /// </summary>
    public int GetVideoCount()
    {
        return videoClips.Count;
    }
}